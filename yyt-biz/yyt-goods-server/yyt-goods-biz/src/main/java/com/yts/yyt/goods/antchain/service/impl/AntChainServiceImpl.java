package com.yts.yyt.goods.antchain.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alipay.mychain.taas.api.common.phase.PhaseItem;
import com.alipay.mychain.taas.api.common.phase.Uploader;
import com.alipay.mychain.taas.api.common.stock.StockPhaseSaveInfo;
import com.alipay.mychain.taas.api.enums.PhaseDisplay;
import com.alipay.mychain.taas.api.enums.PhaseType;
import com.alipay.mychain.taas.api.request.BatchesQueryReq;
import com.alipay.mychain.taas.api.request.BatchesRegReq;
import com.alipay.mychain.taas.api.request.PageReq;
import com.alipay.mychain.taas.api.request.TraceCertQueryReq;
import com.alipay.mychain.taas.api.request.TraceCodeRegReq;
import com.alipay.mychain.taas.api.request.stock.stockPhase.StockPhaseAddReq;
import com.alipay.mychain.taas.api.request.stock.stockPhase.StockPhaseModifyReq;
import com.alipay.mychain.taas.api.request.stock.stockPhase.StockPhaseQueryReq;
import com.alipay.mychain.taas.api.request.stock.stockPhase.StockPhaseSaveAllReq;
import com.alipay.mychain.taas.api.request.stock.stockPhase.StockPhaseUpChainReq;
import com.alipay.mychain.taas.api.response.BatchesResp;
import com.alipay.mychain.taas.api.response.CommonResp;
import com.alipay.mychain.taas.api.response.PageResp;
import com.alipay.mychain.taas.api.response.TraceCertResp;
import com.alipay.mychain.taas.api.response.stock.StockPhaseResp;
import com.alipay.mychain.taas.sdk.service.TraceCoreService;
import com.alipay.mychain.taas.sdk.service.TsBatchService;
import com.alipay.mychain.taas.sdk.service.TsPhaseService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.alert.annotation.AlertException;
import com.yts.yyt.common.alert.enums.AlertLevel;
import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.goods.antchain.dto.CertQueryConditionDTO;
import com.yts.yyt.goods.antchain.dto.ChainContentDTO;
import com.yts.yyt.goods.antchain.dto.ProductUpChainDTO;
import com.yts.yyt.goods.antchain.dto.ReigsterBatchResultDTO;
import com.yts.yyt.goods.antchain.service.AntChainService;
import com.yts.yyt.goods.api.entity.GoodsChainBatchEntity;
import com.yts.yyt.goods.api.entity.GoodsChainPendingEntity;
import com.yts.yyt.goods.api.entity.GoodsChainPhaseEntity;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.GoodsUpChainExcMsg;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.PendingBizType;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.PendingStatus;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.PhaseEffect;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.PhaseUpChainStatus;
import com.yts.yyt.goods.api.enums.GoodsChainEnum.RegisterStatus;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.service.GoodsChainBatchService;
import com.yts.yyt.goods.service.GoodsChainPendingService;
import com.yts.yyt.goods.service.GoodsChainPhaseService;
import com.yts.yyt.goods.service.GoodsChainTracecodeService;
import com.yts.yyt.goods.service.GoodsTraceabilityPreService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AntChainServiceImpl implements AntChainService {

    @Value("${antchain.product-no}")
    private String productNo;

    @Value("${antchain.merchant-id}")
    private String merchantId;

    @Autowired
    private GoodsChainBatchService goodsChainBatchService;

    @Autowired
    private GoodsChainPendingService goodsChainPendingService;

    @Autowired
    private GoodsChainPhaseService goodsChainPhaseService;

    @Autowired
    private GoodsChainTracecodeService goodsChainTracecodeService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TsBatchService tsBatchService;

    @Autowired
    private TsPhaseService tsPhaseService;

    @Autowired
    private TraceCoreService traceCoreService;

    @Autowired
    private GoodsTraceabilityPreService goodsTraceabilityPreService;


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void publishAntChain(ProductUpChainDTO upChainDTO) {
        log.info("【开始对当前商品信息上链】：：：goodsNo={}, ProductUpChainDTO={}", upChainDTO.getGoodsNo(), upChainDTO.toString());
        ChainContentDTO chainContent = new ChainContentDTO();
        chainContent.setGoodsName(upChainDTO.getGoodsName())
                .setYears(upChainDTO.getYears())
                .setOriginPlace(upChainDTO.getOriginPlace())
                .setCondition(upChainDTO.getCondition())
                .setSizeContent(upChainDTO.getSizeContent())
                .setProductNo(upChainDTO.getGoodsNo())
                .setMianImageUrl(upChainDTO.getMianImageUrl())
                .setPreviewImageUrl(upChainDTO.getPreviewImageUrl())
                .setFrontImageUrl(upChainDTO.getFrontImageUrl())
                .setSideImageUrl(upChainDTO.getSideImageUrl())
                .setBackImageUrl(upChainDTO.getBackImageUrl())
                .setIdentity(upChainDTO.getIdentity())
                .setCertHash(upChainDTO.getCertHash());
        String chainContentStr = JSON.toJSONString(chainContent);

        GoodsChainBatchEntity goodsChainBatchEntity = new GoodsChainBatchEntity();
        LocalDateTime nowTime = LocalDateTime.now();
        goodsChainBatchEntity.setId(IDS.uniqueID())
                .setGoodsNo(upChainDTO.getGoodsNo())
                .setGoodsName(upChainDTO.getGoodsName())
                .setChainProductId(productNo)
                .setMerchantId(merchantId)
                .setChainContent(chainContentStr)
                .setStatus(RegisterStatus.IN_EXECUTION.getCode())
                .setCreateTime(nowTime);

        GoodsChainPendingEntity goodsChainPendingEntity = new GoodsChainPendingEntity();
        goodsChainPendingEntity.setId(IDS.uniqueID())
                .setBizId(String.valueOf(goodsChainBatchEntity.getId()))
                .setBizType(PendingBizType.PRODUCT_UPCHAIN.getCode())
                .setStatus(PendingStatus.NON_EXECUTION.getCode())
                .setCreateTime(nowTime);

        try {
            log.info("【保存商品上链记录】：：：goodsNo={}, GoodsChainBatchEntity={}, goodsChainPendingEntity={}",
                    upChainDTO.getGoodsNo(), goodsChainBatchEntity.toString(), goodsChainPendingEntity.toString());
            goodsChainBatchService.save(goodsChainBatchEntity);
            goodsChainPendingService.save(goodsChainPendingEntity);
        } catch (DuplicateKeyException e) {
            log.warn("【重复保存商品上链批次信息】：：：goodsNo={}, GoodsChainBatchEntity={}", upChainDTO.getGoodsNo(), goodsChainBatchEntity.toString(), e);
        }
    }



    public void consumerPendingOfLock() {
        RLock lock = null;
        try {
            lock = redissonClient.getLock(APCHAIN_LOCK_KEY);
            boolean success = getLock(lock);
            if(success) {
                consumerUpchainPending();
            }
        } catch (Exception e) {
            log.error("【本次任务执行-商品上链失败】", e);
        } finally {
            unlock(lock);
        }
    }

    private final int PAGE_SIZE = 20;
    private final int PAGE_NUM = 1;

	@AlertException(value ="商品上链任务",modules = ServiceNameConstants.GOODS_SERVER ,level = AlertLevel.CRITICAL)
	public void consumerUpchainPending() {
        log.info("【开始执行本次商品上链任务】");
        Page pageParam = new Page(PAGE_NUM, PAGE_SIZE);
        LambdaQueryWrapper<GoodsChainPendingEntity> lambdaQueryWrapper = new LambdaQueryWrapper<GoodsChainPendingEntity>()
                .eq(GoodsChainPendingEntity::getBizType, PendingBizType.PRODUCT_UPCHAIN.getCode())
                .eq(GoodsChainPendingEntity::getStatus, PendingStatus.NON_EXECUTION.getCode())
                .orderByAsc(GoodsChainPendingEntity::getCreateTime);
        Page<GoodsChainPendingEntity> pendingPage = goodsChainPendingService.page(pageParam, lambdaQueryWrapper);
        if(pendingPage.getRecords() == null || pendingPage.getRecords().size() == 0) {
            log.info("【本次上链任务执行 - 没有要上链的商品】");
            return ;
        }
        long pages = pendingPage.getPages();
        long total = pendingPage.getTotal();
        log.info("【开始执行本次上链任务】：：：pages={}, total={}", pages, total);
        for (long i = 1; i <= pages; i++) {
            log.info("【开始执行第{}页数据上链】", i);
            List<GoodsChainPendingEntity> pendingList = null;
            if(i == 1) {
                pendingList = pendingPage.getRecords();
            } else {
                Page<GoodsChainPendingEntity> tmpPendingPage = goodsChainPendingService.page(pageParam, lambdaQueryWrapper);
                pendingList = tmpPendingPage.getRecords();
            }

            if(pendingList != null && pendingList.size() > 0) {
                for (GoodsChainPendingEntity record : pendingList) {
                    GoodsChainBatchEntity batchEntity = goodsChainBatchService.getById(record.getBizId());
					// 处理已存在的批次
//					handleExistBatch(batchEntity);

					handleUpChain(record);
                }
            }
        }
    }


    public void handleUpChain(GoodsChainPendingEntity record) {
        log.info("【开始上链】：：：GoodsChainPendingEntity={}", record.toString());
        try {
            long batchPriId = Long.valueOf(record.getBizId());
            //开始发布商品批次
            ReigsterBatchResultDTO batchResultDTO = registerBatch(record.getId(), batchPriId);

            //发布批次环节
            Long phasePriId = registerPhase(batchPriId, PhaseDisplay.USER,PhaseType.COMMON_PHASE);

            //上链
            String txHash = registerChain(phasePriId);

            //上传批次溯源码
            String traceCode = registerTraceCode(batchPriId, batchResultDTO.getBatchId());

            //查询区块信息
            queryBlockCert(phasePriId, txHash);

            //通知商品 上链成功
            goodsTraceabilityPreService.updateTraceStatus(batchResultDTO.getGoodsNo(), traceCode, 1, txHash);

            log.info("【商品上链成功，移除Pending记录】：：：GoodsChainPendingEntity={}", record.toString());
            goodsChainPendingService.removeById(record.getId());
        } catch (Exception e) {
            goodsChainPendingService.updatePendingStatus(record.getId(), PendingStatus.FAIL);
            log.error("【上链失败】：：：GoodsChainPendingEntity={}", record.toString(), e);
        }
    }

    public ReigsterBatchResultDTO registerBatch(long pendingId, long batchPrimaryId) {
        log.info("【开始创建商品批次】：：：pendingId={}, batchPrimaryId={}", pendingId, batchPrimaryId);
        ReigsterBatchResultDTO resultDTO = new ReigsterBatchResultDTO();
        //查询商品批次记录
        GoodsChainBatchEntity batchEntity = goodsChainBatchService.getById(batchPrimaryId);
        if(batchEntity == null) {
            log.warn("【上链批次信息不存在】：：：pendingId={}, batchPrimaryId={}", pendingId, batchPrimaryId);
            throw new GoodsException(GoodsUpChainExcMsg.NO_BATCH_RECORD.getCode(), GoodsUpChainExcMsg.NO_BATCH_RECORD.getDesc());
        }

        if(batchEntity.getStatus().byteValue() == RegisterStatus.SUCCEED.getCode()) {
            log.warn("【当前商品批次已发布】：：：pendingId={}, batchPrimaryId={}", pendingId, batchPrimaryId);
            resultDTO.setBatchId(batchEntity.getBatchId())
                     .setGoodsNo(batchEntity.getGoodsNo());
            return resultDTO;
        }

        //注册商品批次
        String batchId = reqRegisterBatch(batchEntity.getGoodsNo(), batchEntity.getGoodsName());
        if(StringUtils.isNotEmpty(batchId)) {
            goodsChainBatchService.registerBatchCompleted(batchEntity.getId(), batchId);
            resultDTO.setBatchId(batchId)
                    .setGoodsNo(batchEntity.getGoodsNo());
        } else {
            goodsChainBatchService.updateBatchStatus(batchEntity.getId(), RegisterStatus.FAIL);
            throw new GoodsException(GoodsUpChainExcMsg.FAIL_REGISTER_BATCH.getCode(), GoodsUpChainExcMsg.FAIL_REGISTER_BATCH.getDesc());
        }
        return resultDTO;
    }


    private String reqRegisterBatch(String goodsNo, String goodsName) {
        log.info("【开始发送创建批次请求】：：：goodsNo={}, goodsName={}", goodsNo, goodsName);
        String batchId = null;
        try {
            BatchesRegReq batchesRegReq = new BatchesRegReq();
            batchesRegReq.setName(goodsNo);
            batchesRegReq.setProductId(productNo);
            batchesRegReq.setRemark(goodsName);
            batchesRegReq.setOperator("System");
            log.info("【创建批次请求参数】：：：reqParam={}", batchesRegReq.toString());
            CommonResp<String> stringCommonResp = tsBatchService.registerBatch(batchesRegReq);
            if(stringCommonResp != null) {
                log.info("【创建批次请求处理结果】：：：goodsNo={}, goodsName={}, CommonResp={}", goodsNo, goodsName, stringCommonResp.toString());
                if(stringCommonResp.isSuccess()) {
                    batchId = stringCommonResp.getData();
                } else if("AE0310018002".equalsIgnoreCase(stringCommonResp.getCode())){
                    //查询已存在的批次id
                    batchId = getBatchIdByProductId(goodsNo);
                }
            }
        } catch (Exception e) {
            log.error("【创建批次请求失败】：：：goodsNo={}, goodsName={}", goodsNo, goodsName, e);
        }
        return batchId;
    }

    private String getBatchIdByProductId(String goodsNo) {
        log.info("【根据商品id查询批次信息】：：：goodsNo={}", goodsNo);
        String batchId = null;
        try {
            PageReq<BatchesQueryReq> req = new PageReq<>();
            BatchesQueryReq batchesRegReq = new BatchesQueryReq();
            batchesRegReq.setName(goodsNo);
            batchesRegReq.setMerchantId(merchantId);
            batchesRegReq.setProductId(productNo);
            req.setNum(1);
            req.setSize(1);
            req.setData(batchesRegReq);
            CommonResp<PageResp<BatchesResp>> pageRespCommonResp = tsBatchService.listBatch(req);
            if(pageRespCommonResp != null && pageRespCommonResp.isSuccess()) {
                log.info("【根据商品id查询批次结果】：：：CommonResp={}", pageRespCommonResp.toString());
                PageResp<BatchesResp> data = pageRespCommonResp.getData();
                if(data != null && data.getData() != null && data.getData().size() > 0) {
                    BatchesResp batchesResp = data.getData().get(0);
                    batchId = batchesResp.getBatchId();
                }
            }
        } catch (Exception e) {
            log.error("【根据商品id查询批次信息失败】：：：goodsNo={}", goodsNo, e);
        }
        return batchId;
    }



    /**
     *
     * @param batchPrimaryId
     * @param viewType
     */
    @Override
    public Long registerPhase(long batchPrimaryId, PhaseDisplay viewType,PhaseType phaseType) {
        log.info("【开始注册环节（商品信息）】：：：id = {}, viewType={}", batchPrimaryId, viewType);
        GoodsChainBatchEntity batchEntity = goodsChainBatchService.getById(batchPrimaryId);
        GoodsChainPhaseEntity phaseEntity = goodsChainPhaseService.getByBatchPriId(batchEntity.getId(), PhaseEffect.PRO_INFO);

        if(phaseEntity != null && phaseEntity.getStatus().byteValue() == RegisterStatus.SUCCEED.getCode()) {
            log.warn("【当前商品批次环节已经注册】：：：id={}, batchId={}", phaseEntity.getId(), phaseEntity.getBatchId());
            return phaseEntity.getId();
        }

        if(phaseEntity != null && phaseEntity.getStatus().byteValue() == RegisterStatus.IN_EXECUTION.getCode()) {
            String batchId = batchEntity.getBatchId();
            String stockPhaseId = getStockPhaseByBatchIdAndName(batchId, batchEntity.getGoodsName());
            if(StringUtils.isNotEmpty(stockPhaseId)) {
                goodsChainPhaseService.registerPhaseCompleted(phaseEntity.getId(), stockPhaseId);
                return phaseEntity.getId();
            }
        }

        StockPhaseAddReq stockPhaseAddReq = buildStockPhaseAddReq(batchEntity,phaseType);
        Long id = null;
        if(phaseEntity == null) {
            id = goodsChainPhaseService.initSave(batchEntity, stockPhaseAddReq);
        } else {
            id = phaseEntity.getId();
        }
        String phaseId = reqRegisterPhaseAll(stockPhaseAddReq);
        if(StringUtils.isNotEmpty(phaseId)) {
            goodsChainPhaseService.registerPhaseCompleted(id, phaseId);
        } else {
            goodsChainPhaseService.updatePhaseStatus(id, RegisterStatus.FAIL);
            throw new GoodsException(GoodsUpChainExcMsg.FAIL_REGISTER_PHASE.getCode(), GoodsUpChainExcMsg.FAIL_REGISTER_PHASE.getDesc());
        }
        return id;
    }

	private String reqRegisterPhase(StockPhaseAddReq req) {
		log.info("【开始发送创建环节请求】：：：batchId={}", req.getBatchId());
		String phaseId = null;
		try {
			log.info("【创建环节请求参数】：：：reqParam={}", req.toString());
			CommonResp<String> stringCommonResp = tsPhaseService.add(req);
			if(stringCommonResp != null) {
				log.info("【创建环节请求处理结果】：：：batchId={}, CommonResp={}", req.getBatchId(), stringCommonResp.toString());
				if(stringCommonResp.isSuccess()) {
					phaseId = stringCommonResp.getData();
				}
			}
		} catch (Exception e) {
			log.error("【请求创建环节失败】：：：batchId={}", req.getBatchId(), e);
		}
		return phaseId;
	}


    private String reqRegisterPhaseAll(StockPhaseAddReq req) {
		StockPhaseQueryReq queryReq = new StockPhaseQueryReq();
		queryReq.setBatchId(req.getBatchId());
		log.info("【查询批次环节信息】：：：batchId={}", req.getBatchId());
		CommonResp<List<StockPhaseResp>> list = tsPhaseService.list(queryReq);
		List<StockPhaseResp> data = list.getData();
		if (data.isEmpty()){
			return reqRegisterPhase(req);
		}
		List<StockPhaseSaveInfo> stockPhaseSaveInfoList = new ArrayList<>();
		//处理已上链的数据
		for (StockPhaseResp stockPhaseResp : data) {
			StockPhaseSaveInfo stockPhaseSaveInfo = new StockPhaseSaveInfo();
			stockPhaseSaveInfo.setPhaseId(stockPhaseResp.getPhaseId());
			stockPhaseSaveInfo.setName(stockPhaseResp.getName());
			stockPhaseSaveInfo.setIcon(stockPhaseResp.getIcon());
			stockPhaseSaveInfo.setItemList(stockPhaseResp.getItemList());
			//链上该批次统一为不显示
			stockPhaseSaveInfo.setViewType(PhaseDisplay.INNER);
			stockPhaseSaveInfo.setHideItemIndexes(stockPhaseResp.getHideItemIndexes());
			stockPhaseSaveInfoList.add(stockPhaseSaveInfo);
		}
		//处理准备上链的数据
		StockPhaseSaveInfo add = new StockPhaseSaveInfo();
		add.setName(req.getName());
		add.setIcon(req.getIcon());
		add.setItemList(req.getItemList());
		add.setViewType(PhaseDisplay.USER);
		stockPhaseSaveInfoList.add(add);

		StockPhaseSaveAllReq stockPhaseSaveAllReq = new StockPhaseSaveAllReq();
		stockPhaseSaveAllReq.setBatchId(req.getBatchId());
		Uploader uploader = new Uploader();
		uploader.identity("贵州博央文物有限公司")
				.certHash("91520102MADXHMXE85")
				.uploadTime(System.currentTimeMillis());
		stockPhaseSaveAllReq.setUploader(uploader);

		stockPhaseSaveAllReq.setPhaseInfoList(stockPhaseSaveInfoList);
		log.info("【开始发送创建环节请求】：：：batchId={}", req.getBatchId());
        String phaseId = null;
        try {
            log.info("【创建环节请求参数】：：：reqParam={}", req.toString());
			CommonResp<List<StockPhaseResp>> phaseResp = tsPhaseService.saveAll(stockPhaseSaveAllReq);
            if(phaseResp != null) {
                log.info("【创建环节请求处理结果】：：：batchId={}, CommonResp={}", req.getBatchId(), phaseResp.toString());
                if(phaseResp.isSuccess()) {
                    phaseId = phaseResp.getData().get(phaseResp.getData().size() - 1).getPhaseId();
                }
            }
        } catch (Exception e) {
            log.error("【请求创建环节失败】：：：batchId={}", req.getBatchId(), e);
        }
        return phaseId;
    }


    private String getStockPhaseByBatchIdAndName(String batchId, String name) {
        log.info("【根据批次Id查询环节信息】：：：batchId={}, name={}", batchId, name);
        String phaseId = null;
        try {
            StockPhaseQueryReq queryReq = new StockPhaseQueryReq();
            queryReq.setBatchId(batchId);
            queryReq.setName(name);
            CommonResp<List<StockPhaseResp>> phaseRespList = tsPhaseService.list(queryReq);
            if(phaseRespList != null && phaseRespList.isSuccess()) {
                log.info("【根据批次id查询环节结果】：：：CommonResp={}", phaseRespList.toString());
                List<StockPhaseResp> data1 = phaseRespList.getData();
                if(data1 != null && data1.size() > 0) {
                    for (StockPhaseResp stockPhaseResp : data1) {
                        if(stockPhaseResp.getPhaseType().equals(PhaseType.COMMON_PHASE) && stockPhaseResp.getViewType().equals(PhaseDisplay.USER)) {
                            phaseId = stockPhaseResp.getPhaseId();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("【根据批次id查询环节信息失败】：：：batchId={}, name={}", batchId, name, e);
        }
        return phaseId;
    }

    private StockPhaseAddReq buildStockPhaseAddReq(GoodsChainBatchEntity batchEntity,PhaseType phaseType) {
        StockPhaseAddReq stockPhaseAddReq = new StockPhaseAddReq();
        stockPhaseAddReq.setBatchId(batchEntity.getBatchId());
        stockPhaseAddReq.setPhaseType(phaseType);//商品信息环节
        stockPhaseAddReq.setName(batchEntity.getGoodsName());//环节名称
        stockPhaseAddReq.setViewType(PhaseDisplay.USER);//
        stockPhaseAddReq.setSortNo(1L);
        String chainContentStr = batchEntity.getChainContent();
        ChainContentDTO chainContent = JSON.parseObject(chainContentStr, ChainContentDTO.class);

        //stockPhaseAddReq.setIcon(chainContent.getPreviewImageUrl());
        //溯源环节项
        ArrayList<PhaseItem> phaseItems = new ArrayList<>();
        PhaseItem phaseItem = PhaseItem.buildTextItem("【年代】", chainContent.getYears());
        phaseItems.add(phaseItem);

        if(StringUtils.isNotEmpty(chainContent.getOriginPlace())) {
            PhaseItem phaseItem1 = PhaseItem.buildTextItem("【窑口】", chainContent.getOriginPlace());
            phaseItems.add(phaseItem1);
        }

        if(StringUtils.isNotEmpty(chainContent.getSizeContent())) {
            PhaseItem phaseItem3 = PhaseItem.buildTextItem("【尺寸】", chainContent.getSizeContent());
            phaseItems.add(phaseItem3);
        }

        PhaseItem phaseItem6 = PhaseItem.buildTextItem("【产品编号】", chainContent.getProductNo());
        phaseItems.add(phaseItem6);

        PhaseItem phaseItem4 = PhaseItem.buildPurePicItem("【商品主图】", Collections.singletonList(chainContent.getMianImageUrl()));
        phaseItems.add(phaseItem4);

        List<String> detailPicList = new ArrayList<>();
        if(StringUtils.isNotEmpty(chainContent.getFrontImageUrl())) {
            detailPicList.add(chainContent.getFrontImageUrl());
        }
        if(StringUtils.isNotEmpty(chainContent.getBackImageUrl())) {
            detailPicList.add(chainContent.getBackImageUrl());
        }
        if(StringUtils.isNotEmpty(chainContent.getSideImageUrl())) {
            detailPicList.add(chainContent.getSideImageUrl());
        }
        if(detailPicList.size() > 0) {
            PhaseItem phaseItem5 = PhaseItem.buildPurePicItem("【商品详情图】", detailPicList);
            phaseItems.add(phaseItem5);
        }

        stockPhaseAddReq.setItemList(phaseItems);
        return stockPhaseAddReq;
    }

    @Override
    public String registerChain(long phasePriId) {
        log.info("【开始对环节上链】：：：phasePriId={}", phasePriId);
        GoodsChainPhaseEntity phaseEntity = goodsChainPhaseService.getById(phasePriId);
        if(phaseEntity == null) {
            log.warn("【指定批次环节不存在】：：：phasePriId={}", phasePriId);
            throw new GoodsException(GoodsUpChainExcMsg.NO_EXIST_PHASE.getCode(), GoodsUpChainExcMsg.NO_EXIST_PHASE.getDesc());
        }
        if(phaseEntity.getUpChain().byteValue() == PhaseUpChainStatus.Y.getCode()) {
            log.warn("【当前环节已上链】：：：phasePriId={}", phasePriId);
            return phaseEntity.getBlockTxhash();
        }
        if(StringUtils.isNotEmpty(phaseEntity.getBlockTxhash()) &&
                phaseEntity.getUpChain().byteValue() == PhaseUpChainStatus.N.getCode()) {
            log.warn("【当前环节已经上链】：：：phasePriId={}, txHash={}", phasePriId, phaseEntity.getBlockTxhash());
            goodsChainPhaseService.updatePhaseUpChainStatus(phasePriId, PhaseUpChainStatus.Y);
            return phaseEntity.getBlockTxhash();
        }

        GoodsChainBatchEntity batchEntity = goodsChainBatchService.getById(phaseEntity.getBatchId());

        String phaseId = phaseEntity.getPhaseId();
        String chainContentStr = batchEntity.getChainContent();
        ChainContentDTO chainContent = JSON.parseObject(chainContentStr, ChainContentDTO.class);
        String identity = chainContent.getIdentity();
        String certHash = chainContent.getCertHash();

        String txHash = reqRegisterChain(phaseId, identity, certHash);
        if(StringUtils.isEmpty(txHash)) {
            log.error("【环节上链失败】：：：phaseId={}", phaseId);
            throw new GoodsException(GoodsUpChainExcMsg.FAIL_REGISTER_UPCHAIN.getCode(), GoodsUpChainExcMsg.FAIL_REGISTER_UPCHAIN.getDesc());
        }
        goodsChainPhaseService.registerUpChainCompleted(phasePriId, txHash);
        return txHash;
    }



    private String reqRegisterChain(String phaseId, String identity, String certHash) {
        log.info("【开始发送环节上链请求】：：：phaseId={}, identity={}, certHash={}", phaseId, identity, certHash);
        String txHash = null;
        try {
            StockPhaseUpChainReq stockPhaseUpChainReq = new StockPhaseUpChainReq();
            stockPhaseUpChainReq.setPhaseId(phaseId);
            Uploader uploader = new Uploader();
            uploader.identity(identity)
                    .certHash(certHash)
                    .uploadTime(System.currentTimeMillis());
            stockPhaseUpChainReq.setUploader(uploader);
            log.info("【环节上链请求参数】：：：reqParam={}", stockPhaseUpChainReq.toString());
            CommonResp<String> txHashCommonResp = tsPhaseService.upChain(stockPhaseUpChainReq);
            if(txHashCommonResp != null) {
                log.info("【环节上链请求结果】：：：phaseId={}, CommonResp={}", phaseId, txHashCommonResp.toString());
                if(txHashCommonResp.isSuccess()) {
                    txHash = txHashCommonResp.getData();
                    log.info("【批次环节上链成功】：：：phaseId={}, txHash={}", phaseId, txHash);
                } else if("AE0310029004".equalsIgnoreCase(txHashCommonResp.getCode())) {
                    //https://tschain.cloud.alipay.com/api/h5/code
                    log.error("【当前环节已经上链】：：：phaseId={}, identity={}, certHash={}", phaseId, identity, certHash);
                }
            }
        } catch (Exception e) {
            log.error("【环节上链失败】：：：phaseId={}, identity={}, certHash={}", phaseId, identity, certHash, e);
        }
        return txHash;
    }


    public String registerTraceCode(long batchPriId, String batchId) {
        log.info("【开始上传溯源码】：：：batchPriId={}, batchId={}", batchPriId, batchId);

        String tracecode = null;
        GoodsChainBatchEntity batchEntity = goodsChainBatchService.getById(batchPriId);
        if(batchEntity != null && StringUtils.isNotEmpty(batchEntity.getTraceCode())) {
            tracecode = batchEntity.getTraceCode(); //如果当前批次已经绑定溯源码则不需要再次绑定
        } else {
            tracecode = goodsChainBatchService.bindTracecodeToBatch(batchPriId); //从溯源码池子拿取一个溯源码，绑定到批次。
        }

        //然后上传溯源码
        reqRegisterTraceCode(batchId, tracecode);
        return tracecode;
    }


    private void reqRegisterTraceCode(String batchId, String traceCode) {
        log.info("【开始发送上传溯源码请求】：：：batchId={}, traceCode={}", batchId, traceCode);
        try {
            TraceCodeRegReq traceCodeRegReq = new TraceCodeRegReq();
            traceCodeRegReq.merchantId(merchantId)
                    .productId(productNo)
                    .productBatchId(batchId)
                    .code(traceCode)
                    .setRequestNo(String.valueOf(IDS.uniqueID()));
            log.info("【上传溯源码请求参数】：：：reqParam={}", traceCodeRegReq.toString());
            CommonResp<String> stringCommonResp = traceCoreService.uploadTraceCode(traceCodeRegReq);
            if(stringCommonResp != null) {
                log.info("【上传溯源码请求响应结果】：：：CommonResp={}", stringCommonResp.toString());
                if(!stringCommonResp.isSuccess()) {
                    throw new GoodsException();
                }
            }
        } catch (Exception e) {
            log.error("【上传溯源码失败】：：：batchId={}, traceCode={}", batchId, traceCode, e);
            throw new GoodsException(GoodsUpChainExcMsg.FAIL_UP_TRACECODE.getCode(),
                    GoodsUpChainExcMsg.FAIL_UP_TRACECODE.getDesc());
        }
    }


    public void queryBlockCert(long phasePriId, String txHash) {
        try {
            log.info("【开始查询认证区块信息】：：：phasePriId={}, txHash={}", phasePriId, txHash);
            TraceCertResp resp = reqQueryBlockCert(merchantId, txHash);
            if(resp != null) {
                goodsChainPhaseService.updateCertBlockInfo(phasePriId, resp);
            }
        } catch (Exception e) {
            log.error("【查询认证区块信息失败】：：：phasePriId={}, txHash={}",phasePriId, txHash, e);
        }
    }

    private TraceCertResp reqQueryBlockCert(String merchantId, String txHash) {
        log.info("【开始发起查询认证区块信息请求】：：：merchantId={}, txHash={}", merchantId, txHash);
        TraceCertResp resp = null;
        try {
            TraceCertQueryReq traceCertQueryReq = new TraceCertQueryReq();
            traceCertQueryReq.setMerchantId(merchantId);
            traceCertQueryReq.setTxHash(txHash);
            log.info("【发起查询认证区块信息请求参数】：：：traceCertQueryReq={}", traceCertQueryReq.toString());
            CommonResp<TraceCertResp> commonResp = traceCoreService.cert(traceCertQueryReq);
            if(commonResp != null) {
                log.info("【发起查询认证区块信息响应结果】：：：CommonResp={}", commonResp.toString());
                if(commonResp.isSuccess()) {
                    resp = commonResp.getData();
                }
            }
        } catch (Exception e) {
            log.error("【发起查询认证区块信息请求失败】：：：merchantId={}, txHash={}", merchantId, txHash);
        }
        return resp;
    }



    @Override
    public CertQueryConditionDTO queryCertQueryCondition(String goodsNo) {
        CertQueryConditionDTO certQueryConditionDTO = goodsChainBatchService.queryCertQueryCondition(goodsNo);
        return certQueryConditionDTO;
    }

    protected boolean getLock(RLock lock) {
        boolean success = false;
        try {
            success = lock.tryLock(2, 1800, TimeUnit.SECONDS);
            if(!success) {
                log.error("【消费商品上链pending记录任务执行，获取锁失败】");
            }
        } catch (Exception e) {
            log.error("【消费商品上链pending记录任务执行，获取锁异常】", e);
        }
        return success;
    }

    protected void unlock(RLock lock) {
        try {
            if(lock != null && lock.isHeldByCurrentThread()) {
                log.info("【释放消费商品上链pending记录锁】");
                lock.unlock();
            } else {
                log.warn("【当前线程不持有当前消费商品上链pending记录锁】");
            }
        } catch (Exception e) {
            log.error("【释放消费商品上链pending记录锁失败】", e);
        }
    }

    /**
     * 处理已存在的批次
     * @param batchEntity 已存在的批次实体
     */
    private void handleExistBatch(GoodsChainBatchEntity batchEntity) {
        log.info("【开始处理已存在的批次】：：：goodsNo={}", batchEntity.getGoodsNo());
        try {
            // 根据商品编码查询所有批次
            List<GoodsChainBatchEntity> batchList = goodsChainBatchService.list(
                new QueryWrapper<GoodsChainBatchEntity>().lambda()
                    .eq(GoodsChainBatchEntity::getGoodsNo, batchEntity.getGoodsNo())
            );

            if (batchList == null || batchList.isEmpty()) {
                log.warn("【未查询到商品相关批次】：：：goodsNo={}", batchEntity.getGoodsNo());
                return;
            }

            log.info("【查询到商品相关批次数量】：：：goodsNo={}, batchCount={}", batchEntity.getGoodsNo(), batchList.size());

            // 遍历处理每个批次
            for (GoodsChainBatchEntity batch : batchList) {
                if (StringUtils.isEmpty(batch.getBatchId())) {
                    log.warn("【批次ID为空，跳过处理】：：：goodsNo={}, batchId={}", batch.getGoodsNo(), batch.getBatchId());
                    continue;
                }

                log.info("【开始处理批次】：：：goodsNo={}, batchId={}", batch.getGoodsNo(), batch.getBatchId());

                // 更新该批次的所有环节信息
                StockPhaseQueryReq queryReq = new StockPhaseQueryReq();
                queryReq.setBatchId(batch.getBatchId());
                log.info("【查询批次环节信息】：：：batchId={}", batch.getBatchId());
                CommonResp<List<StockPhaseResp>> list = tsPhaseService.list(queryReq);
                if (list != null && list.isSuccess()) {
                    List<StockPhaseResp> data = list.getData();
                    log.info("【查询到环节数量】：：：batchId={}, phaseCount={}", batch.getBatchId(), data.size());
                    for (StockPhaseResp stockPhaseResp : data) {
						if (stockPhaseResp.getViewType().equals(PhaseDisplay.INNER)){
							continue;
						}
                        log.info("【开始修改环节显示状态】：：：phaseId={}, phaseName={}", stockPhaseResp.getPhaseId(), stockPhaseResp.getName());
                        StockPhaseModifyReq modifyReq = new StockPhaseModifyReq();
                        modifyReq.setPhaseId(stockPhaseResp.getPhaseId());
                        modifyReq.setViewType(PhaseDisplay.INNER);
						modifyReq.setName(stockPhaseResp.getName());
						modifyReq.setItemList(stockPhaseResp.getItemList());
						modifyReq.setHideItemIndexes(stockPhaseResp.getHideItemIndexes());
						modifyReq.setIcon(stockPhaseResp.getIcon());
						modifyReq.setPhaseType(stockPhaseResp.getPhaseType());
						modifyReq.setSortNo(stockPhaseResp.getSortNo());
						modifyReq.setStatus(stockPhaseResp.getStatus());
                        CommonResp<String> modifyResp = tsPhaseService.modify(modifyReq);
                        if (modifyResp != null && modifyResp.isSuccess()) {
                            log.info("【环节显示状态修改成功】：：：phaseId={}, phaseName={}", stockPhaseResp.getPhaseId(), stockPhaseResp.getName());
                        } else {
                            log.error("【环节显示状态修改失败】：：：phaseId={}, phaseName={}, resp={}",
                                stockPhaseResp.getPhaseId(), stockPhaseResp.getName(), modifyResp);
                        }
                    }
                    log.info("【批次环节处理完成】：：：goodsNo={}, batchId={}", batch.getGoodsNo(), batch.getBatchId());
                } else {
                    log.error("【查询批次环节信息失败】：：：batchId={}, resp={}", batch.getBatchId(), list);
                }
            }
            log.info("【所有批次处理完成】：：：goodsNo={}", batchEntity.getGoodsNo());
        } catch (Exception e) {
            log.error("【处理已存在批次异常】：：：goodsNo={}", batchEntity.getGoodsNo(), e);
        }
    }

}
