package com.yts.yyt.goods.mq.interceptor;

import com.plumelog.core.TraceId;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * RocketMQ TraceId 统一拦截器
 * 
 * 功能描述：
 * 1. 生产者端：拦截消息发送过程，自动将当前线程的TraceId添加到消息头中，实现链路追踪的传递
 * 2. 消费者端：拦截消息消费过程，从消息头中提取TraceId并设置到当前线程上下文，保持链路追踪的连续性
 * 3. 支持MDC和Plumelog的TraceId机制，确保日志的链路追踪完整性
 * 
 * 使用场景：
 * - 微服务间通过RocketMQ进行异步通信时的链路追踪
 * - 分布式系统中消息队列的日志关联
 * - 问题排查时通过TraceId快速定位相关日志
 * 
 * 技术实现：
 * - 使用Spring AOP的@Around注解拦截目标方法
 * - 通过切点表达式精确匹配RocketMQ的发送和消费方法
 * - 自动处理TraceId的传递和清理，对业务代码无侵入
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Aspect
@Component
@Order(1) // 设置为最高优先级，确保TraceId拦截在其他切面之前执行
@Slf4j
public class RocketMQTraceInterceptor {

    /**
     * TraceId在消息头中的键名
     * 用于在消息的Properties中存储和获取TraceId
     */
    private static final String TRACE_ID_KEY = "traceId";

    /**
     * 拦截生产者发送方法（生产者端TraceId传递）- 修复版
     *
     * 拦截目标：
     * 1. EnvAwareRocketMQTemplate 的所有发送方法（主要调用路径）
     * 2. RocketMQClientTemplate 的发送方法（底层调用）
     *
     * 处理逻辑：
     * 1. 获取当前线程的TraceId（来自Plumelog）
     * 2. 检查消息头中是否已存在TraceId，避免重复添加
     * 3. 将TraceId添加到消息头中，确保传递到消费者端
     * 4. 保持原有的消息发送逻辑不变
     *
     * @param joinPoint AOP连接点，包含方法参数和上下文信息
     * @return 原方法的执行结果（通常是SendResult或类似的发送结果对象）
     * @throws Throwable 原方法可能抛出的任何异常
     */
    @Around("execution(* com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate.send*(..)) || " +
            "execution(* com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate.convertAndSend(..)) || " +
            "execution(* org.apache.rocketmq.client.core.RocketMQClientTemplate.convertAndSend(..)) || " +
            "execution(* org.apache.rocketmq.client.core.RocketMQClientTemplate.send(..)) || " +
            "execution(* org.apache.rocketmq.client.core.RocketMQClientTemplate.syncSendDelayMessage(..)) || " +
            "execution(* org.apache.rocketmq.client.core.RocketMQClientTemplate.sendMessageInTransaction(..))")
    public Object aroundSend(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法参数和方法名
        Object[] args = joinPoint.getArgs();
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        log.debug("拦截到{}的{}方法，参数数量：{}", className, methodName, args.length);

        // 从当前线程获取TraceId（Plumelog框架提供）
        String currentTraceId = TraceId.logTraceID.get();

        // 只有当TraceId存在且不为空时才进行处理
        if (currentTraceId != null && !currentTraceId.trim().isEmpty()) {
            // 根据不同的类和方法处理TraceId
            if ("EnvAwareRocketMQTemplate".equals(className)) {
                handleEnvAwareRocketMQTemplate(args, currentTraceId, methodName);
            } else if (className.contains("RocketMQClientTemplate")) {
                handleRocketMQClientTemplate(args, currentTraceId, methodName);
            }
        } else {
            log.debug("当前线程没有TraceId，跳过处理");
        }

        // 执行原始的发送方法，传递修改后的参数
        return joinPoint.proceed(args);
    }

    /**
     * 处理 EnvAwareRocketMQTemplate 的方法调用
     * EnvAwareRocketMQTemplate 的方法参数模式：
     * - convertAndSend(String baseTopic, Object payload)
     * - convertAndSend(Class, String baseTopic, Object payload)
     * - convertAndSend(String baseTopic, String tag, Object payload)
     * - send(String baseTopic, Message message)
     * - send(Class, String baseTopic, Message message)
     * - sendDelayMsg(String baseTopic, String tag, Object payload, Duration delayTime)
     * - sendMessageInTransaction(Class, String baseTopic, String tag, Object payload)
     */
    private void handleEnvAwareRocketMQTemplate(Object[] args, String traceId, String methodName) {
        log.debug("处理EnvAwareRocketMQTemplate.{}方法，参数数量：{}", methodName, args.length);

        if (args.length == 0) {
            return;
        }

        // 查找payload参数的位置（通常是最后一个参数，除非最后是Duration）
        int payloadIndex = findPayloadIndex(args, methodName);
        if (payloadIndex == -1) {
            log.debug("未找到payload参数，跳过TraceId处理");
            return;
        }

        Object payload = args[payloadIndex];

        if (payload instanceof Message) {
            // 如果payload已经是Message类型，直接处理
            Message<?> message = (Message<?>) payload;
            if (!message.getHeaders().containsKey(TRACE_ID_KEY)) {
                Message<?> messageWithTrace = MessageBuilder.fromMessage(message)
                        .setHeader(TRACE_ID_KEY, traceId)
                        .build();
                args[payloadIndex] = messageWithTrace;
                log.debug("为EnvAwareRocketMQTemplate.{}消息添加traceId: {}", methodName, traceId);
            }
        } else {
            // 如果payload不是Message类型，构建新的Message
            Message<?> messageWithTrace = MessageBuilder.withPayload(payload)
                    .setHeader(TRACE_ID_KEY, traceId)
                    .build();
            args[payloadIndex] = messageWithTrace;
            log.debug("为EnvAwareRocketMQTemplate.{}负载构建Message并添加traceId: {}", methodName, traceId);
        }
    }

    /**
     * 查找payload参数的索引位置
     */
    private int findPayloadIndex(Object[] args, String methodName) {
        // 对于sendDelayMsg方法，最后一个参数是Duration，payload是倒数第二个
        if (methodName.startsWith("sendDelayMsg") && args.length >= 2) {
            Object lastArg = args[args.length - 1];
            if (lastArg instanceof java.time.Duration) {
                return args.length - 2; // payload是倒数第二个参数
            }
        }

        // 对于其他方法，payload通常是最后一个参数
        if (args.length >= 1) {
            return args.length - 1;
        }

        return -1;
    }

    /**
     * 处理 RocketMQClientTemplate 的方法调用（保持原有逻辑）
     */
    private void handleRocketMQClientTemplate(Object[] args, String traceId, String methodName) {
        if ("convertAndSend".equals(methodName)) {
            handleConvertAndSend(args, traceId);
        } else if ("send".equals(methodName)) {
            handleSend(args, traceId);
        } else if ("syncSendDelayMessage".equals(methodName)) {
            handleSyncSendDelayMessage(args, traceId);
        } else if ("sendMessageInTransaction".equals(methodName)) {
            handleSendMessageInTransaction(args, traceId);
        }
    }

    /**
     * 处理convertAndSend方法的TraceId添加
     * 方法签名：convertAndSend(String destination, Object payload)
     */
    private void handleConvertAndSend(Object[] args, String traceId) {
        if (args.length >= 2) {
            // convertAndSend方法的payload参数
            Object payload = args[1];
            
            // 如果payload已经是Message类型，直接处理
            if (payload instanceof Message) {
                Message<?> message = (Message<?>) payload;
                if (!message.getHeaders().containsKey(TRACE_ID_KEY)) {
                    Message<?> messageWithTrace = MessageBuilder.fromMessage(message)
                            .setHeader(TRACE_ID_KEY, traceId)
                            .build();
                    args[1] = messageWithTrace;
                    log.debug("为convertAndSend消息添加traceId: {}, destination: {}", traceId, args[0]);
                }
            } else {
                // 如果payload不是Message类型，构建新的Message
                Message<?> messageWithTrace = MessageBuilder.withPayload(payload)
                        .setHeader(TRACE_ID_KEY, traceId)
                        .build();
                args[1] = messageWithTrace;
                log.debug("为convertAndSend负载构建Message并添加traceId: {}, destination: {}", traceId, args[0]);
            }
        }
    }
    
    /**
     * 处理send方法的TraceId添加
     * 方法签名：send(String destination, Message<?> message)
     */
    private void handleSend(Object[] args, String traceId) {
        if (args.length >= 2 && args[1] instanceof Message) {
            Message<?> message = (Message<?>) args[1];
            
            // 检查消息头中是否已存在traceId，避免重复添加
            if (!message.getHeaders().containsKey(TRACE_ID_KEY)) {
                Message<?> messageWithTrace = MessageBuilder.fromMessage(message)
                        .setHeader(TRACE_ID_KEY, traceId)
                        .build();
                args[1] = messageWithTrace;
                log.debug("为send消息添加traceId: {}, destination: {}", traceId, args[0]);
            }
        }
    }
    
    /**
     * 处理syncSendDelayMessage方法的TraceId添加
     * 方法签名：syncSendDelayMessage(String destination, Object payload, Duration delayTime)
     */
    private void handleSyncSendDelayMessage(Object[] args, String traceId) {
        if (args.length >= 2) {
            Object payload = args[1];
            
            // 如果payload已经是Message类型，直接处理
            if (payload instanceof Message) {
                Message<?> message = (Message<?>) payload;
                if (!message.getHeaders().containsKey(TRACE_ID_KEY)) {
                    Message<?> messageWithTrace = MessageBuilder.fromMessage(message)
                            .setHeader(TRACE_ID_KEY, traceId)
                            .build();
                    args[1] = messageWithTrace;
                    log.debug("为syncSendDelayMessage消息添加traceId: {}, destination: {}", traceId, args[0]);
                }
            } else {
                // 如果payload不是Message类型，构建新的Message
                Message<?> messageWithTrace = MessageBuilder.withPayload(payload)
                        .setHeader(TRACE_ID_KEY, traceId)
                        .build();
                args[1] = messageWithTrace;
                log.debug("为syncSendDelayMessage负载构建Message并添加traceId: {}, destination: {}", traceId, args[0]);
            }
        }
    }
    
    /**
     * 处理sendMessageInTransaction方法的TraceId添加
     * 方法签名：sendMessageInTransaction(String destination, Object payload)
     */
    private void handleSendMessageInTransaction(Object[] args, String traceId) {
        if (args.length >= 2) {
            Object payload = args[1];
            
            // 如果payload已经是Message类型，直接处理
            if (payload instanceof Message) {
                Message<?> message = (Message<?>) payload;
                if (!message.getHeaders().containsKey(TRACE_ID_KEY)) {
                    Message<?> messageWithTrace = MessageBuilder.fromMessage(message)
                            .setHeader(TRACE_ID_KEY, traceId)
                            .build();
                    args[1] = messageWithTrace;
                    log.debug("为sendMessageInTransaction消息添加traceId: {}, destination: {}", traceId, args[0]);
                }
            } else {
                // 如果payload不是Message类型，构建新的Message
                Message<?> messageWithTrace = MessageBuilder.withPayload(payload)
                        .setHeader(TRACE_ID_KEY, traceId)
                        .build();
                args[1] = messageWithTrace;
                log.debug("为sendMessageInTransaction负载构建Message并添加traceId: {}, destination: {}", traceId, args[0]);
            }
        }
    }

    /**
     * 拦截消费者的 consume 方法（消费者端TraceId设置）- 修复版
     *
     * 拦截目标：所有实现了RocketMQListener接口且标注了@RocketMQMessageListener注解的类的consume方法
     * 这样可以精确拦截到实际的消费者实现，而不是接口本身
     *
     * 处理逻辑：
     * 1. 从接收到的消息中提取TraceId
     * 2. 将TraceId设置到当前线程的MDC和Plumelog上下文中
     * 3. 执行原始的消费逻辑
     * 4. 消费完成后清理TraceId，避免线程复用时的数据污染
     *
     * @param joinPoint AOP连接点，包含消费方法的参数和上下文
     * @return 原消费方法的执行结果
     * @throws Throwable 消费过程中可能抛出的任何异常
     */
    @Around("@target(org.apache.rocketmq.client.annotation.RocketMQMessageListener) && " +
            "execution(* *.consume(org.apache.rocketmq.client.apis.message.MessageView))")
    public Object aroundConsume(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        
        // 参数校验：确保第一个参数是MessageView类型（RocketMQ 5.x的消息对象）
        if (args.length > 0 && args[0] instanceof MessageView) {
            MessageView messageView = (MessageView) args[0];

            // 从消息中提取并设置TraceId到当前线程上下文
            String traceId = setTraceIdFromMessage(messageView);
            
            try {
                // 执行原始的消费方法，此时TraceId已设置完成
                Object result = joinPoint.proceed();
                log.debug("消费消息完成，traceId: {}, messageId: {}", traceId, messageView.getMessageId());
                return result;
            } finally {
                // 无论消费成功或失败，都要清理TraceId
                // 这很重要，因为消费者使用的是线程池，线程会被复用
                clearTraceId();
            }
        } else {
            // 如果参数不是MessageView类型，直接执行原方法
            // 这种情况理论上不应该发生，但为了健壮性还是处理一下
            log.warn("消费者方法参数不是MessageView类型，跳过TraceId处理");
            return joinPoint.proceed();
        }
    }

    /**
     * 从消息中提取并设置 TraceId 到当前线程上下文
     * 
     * 处理策略：
     * 1. 优先从消息的Properties中获取traceId
     * 2. 如果消息中没有traceId，则使用messageId作为备选方案
     * 3. 同时设置到MDC（用于logback等日志框架）和Plumelog的TraceId中
     * 
     * @param messageView RocketMQ 5.x的消息视图对象，包含消息内容和属性
     * @return 设置的TraceId值，用于日志记录
     */
    private String setTraceIdFromMessage(MessageView messageView) {
        // 从消息属性中获取TraceId
        String traceId = messageView.getProperties().get(TRACE_ID_KEY);
        
        if (traceId != null && !traceId.trim().isEmpty()) {
            // 场景1：消息中包含有效的TraceId（正常情况）
            // 设置到MDC中，供logback等日志框架使用
            MDC.put(TRACE_ID_KEY, traceId);
            // 设置到Plumelog的ThreadLocal中
            TraceId.logTraceID.set(traceId);
            
            log.debug("从消息中提取并设置traceId: {}, messageId: {}", traceId, messageView.getMessageId());
        } else {
            // 场景2：消息中没有TraceId（可能是外部系统发送的消息或老版本发送的消息）
            // 使用messageId作为备选TraceId，确保日志的可追踪性
            String fallbackTraceId = messageView.getMessageId().toString();
            
            // 设置备选TraceId到上下文中
            MDC.put(TRACE_ID_KEY, fallbackTraceId);
            TraceId.logTraceID.set(fallbackTraceId);
            
            log.debug("消息中未包含traceId，使用messageId作为traceId: {}", fallbackTraceId);
            traceId = fallbackTraceId;
        }
        
        return traceId;
    }

    /**
     * 清理当前线程的 TraceId 上下文
     * 
     * 清理范围：
     * 1. Plumelog的ThreadLocal变量
     * 2. SLF4J的MDC上下文
     * 
     * 重要性：
     * - 防止线程复用时TraceId污染
     * - 避免内存泄漏（ThreadLocal未清理）
     * - 确保每次消费都有独立的TraceId上下文
     * 
     * 调用时机：
     * - 消息消费完成后（在finally块中调用）
     * - 无论消费成功或失败都会执行
     */
    private void clearTraceId() {
        // 清理Plumelog的ThreadLocal
        TraceId.logTraceID.remove();
        // 清理SLF4J的MDC上下文
        MDC.clear();
        
        log.debug("已清理TraceId");
    }
}