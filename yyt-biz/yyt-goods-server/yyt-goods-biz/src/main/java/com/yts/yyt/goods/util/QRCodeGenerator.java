package com.yts.yyt.goods.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class QRCodeGenerator {

	private static final int BASE_QR_SIZE = 1024; // 提高基础分辨率
	private static final float LOGO_SCALE = 0.15f; // 适当减小Logo比例
	private static final int TEXT_HEIGHT = 80;
	private static final String DEFAULT_FONT = "Microsoft YaHei";
	private static final int LOGO_MIN_SIZE = 256; // Logo最小尺寸

	public BufferedImage generateHighQualityQRCode(String content, String serialNumber) {
		try {
			// 1. 生成超高分辨率二维码
			BufferedImage qrImage = generateUltraQualityQRCode(content, BASE_QR_SIZE, BASE_QR_SIZE);

			// 2. 添加超清晰Logo
			qrImage = addUltraSharpLogo(qrImage, "/static/logo.png");

			// 3. 添加抗锯齿文字
			if (StringUtils.isNotBlank(serialNumber)) {
				qrImage = addPremiumText(qrImage, serialNumber);
			}
			return qrImage;
		} catch (Exception e) {
			log.error("生成二维码失败", e);
			throw new RuntimeException("生成二维码失败: " + e.getMessage());
		}
	}

	private BufferedImage generateUltraQualityQRCode(String content, int width, int height) {
		try {
			// 使用更高容错率
			Map<EncodeHintType, Object> hints = new HashMap<>();
			hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
			hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q); // 使用Q级容错
			hints.put(EncodeHintType.MARGIN, 2);

			QRCodeWriter writer = new QRCodeWriter();
			BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

			// 使用更高质量的渲染
			return MatrixToImageWriter.toBufferedImage(bitMatrix, getMatrixConfig());
		} catch (Exception e) {
			throw new RuntimeException("生成二维码失败", e);
		}
	}

	private MatrixToImageConfig getMatrixConfig() {
		// 自定义二维码颜色和对比度
		return new MatrixToImageConfig(0xFF000000, 0xFFFFFFFF);
	}

	private BufferedImage addUltraSharpLogo(BufferedImage qrImage, String logoPath) {
		try {
			ClassPathResource resource = new ClassPathResource(logoPath);
			if (!resource.exists()) {
				log.warn("Logo文件不存在: {}", logoPath);
				return qrImage;
			}

			try (InputStream logoStream = resource.getInputStream()) {
				BufferedImage originalLogo = ImageIO.read(logoStream);
				if (originalLogo == null) {
					log.warn("无法读取Logo图像: {}", logoPath);
					return qrImage;
				}

				// 计算最终Logo尺寸（保持宽高比）
				int logoSize = calculateOptimalLogoSize(qrImage, originalLogo);

				// 高质量缩放Logo
				BufferedImage scaledLogo = resizeWithSuperQuality(originalLogo, logoSize, logoSize);

				// 创建ARGB格式的二维码副本
				BufferedImage combined = new BufferedImage(
						qrImage.getWidth(),
						qrImage.getHeight(),
						BufferedImage.TYPE_INT_ARGB);

				Graphics2D g = combined.createGraphics();
				applyUltraQualitySettings(g);

				// 绘制二维码
				g.drawImage(qrImage, 0, 0, null);

				// 绘制Logo（居中）
				int x = (combined.getWidth() - logoSize) / 2;
				int y = (combined.getHeight() - logoSize) / 2;

				// 添加白色背景边框
				int borderSize = logoSize / 20;
				g.setColor(Color.WHITE);
				g.fillRoundRect(x - borderSize, y - borderSize,
						logoSize + 2*borderSize, logoSize + 2*borderSize,
						borderSize*2, borderSize*2);

				g.drawImage(scaledLogo, x, y, null);
				g.dispose();

				return combined;
			}
		} catch (Exception e) {
			log.warn("添加Logo失败: {}", e.getMessage());
			return qrImage;
		}
	}

	private int calculateOptimalLogoSize(BufferedImage qrImage, BufferedImage logo) {
		// 确保Logo不小于最小尺寸
		int baseSize = (int) (qrImage.getWidth() * LOGO_SCALE);
		baseSize = Math.max(baseSize, LOGO_MIN_SIZE);

		// 根据原始Logo宽高比调整
		float aspectRatio = (float) logo.getWidth() / logo.getHeight();
		if (aspectRatio > 1.1f) {
			return (int) (baseSize * aspectRatio);
		} else if (aspectRatio < 0.9f) {
			return (int) (baseSize / aspectRatio);
		}
		return baseSize;
	}

	private BufferedImage resizeWithSuperQuality(BufferedImage original, int width, int height) {
		// 使用多步缩放技术
		BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
		Graphics2D g = resized.createGraphics();
		applyUltraQualitySettings(g);

		// 分步缩放（先缩放到中间尺寸）
		int intermediateWidth = width * 2;
		int intermediateHeight = height * 2;

		BufferedImage intermediate = new BufferedImage(
				intermediateWidth, intermediateHeight, BufferedImage.TYPE_INT_ARGB);
		Graphics2D g2 = intermediate.createGraphics();
		applyUltraQualitySettings(g2);
		g2.drawImage(original, 0, 0, intermediateWidth, intermediateHeight, null);
		g2.dispose();

		// 最终缩放
		g.drawImage(intermediate, 0, 0, width, height, null);
		g.dispose();

		return resized;
	}

	private void applyUltraQualitySettings(Graphics2D g) {
		g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
		g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
		g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
		g.setRenderingHint(RenderingHints.KEY_ALPHA_INTERPOLATION, RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
		g.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
		g.setRenderingHint(RenderingHints.KEY_DITHERING, RenderingHints.VALUE_DITHER_ENABLE);
		g.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
	}

	private BufferedImage addPremiumText(BufferedImage qrImage, String text) {
		BufferedImage combined = new BufferedImage(
				qrImage.getWidth(),
				qrImage.getHeight() + TEXT_HEIGHT,
				BufferedImage.TYPE_INT_ARGB);

		Graphics2D g = combined.createGraphics();
		applyUltraQualitySettings(g);

		// 绘制白色背景
		g.setColor(Color.WHITE);
		g.fillRect(0, 0, combined.getWidth(), combined.getHeight());

		// 绘制二维码
		g.drawImage(qrImage, 0, 0, null);

		// 设置文字样式
		g.setColor(Color.BLACK);
		Font font = findOptimalFont(g, text, qrImage.getWidth() - 80);
		g.setFont(font);

		// 计算文字位置
		FontMetrics metrics = g.getFontMetrics();
		int textX = (qrImage.getWidth() - metrics.stringWidth(text)) / 2;
		int textY = qrImage.getHeight() + (TEXT_HEIGHT + metrics.getAscent() - metrics.getDescent()) / 2;

		// 添加文字阴影增强可读性
		g.setColor(new Color(0, 0, 0, 100));
		g.drawString(text, textX + 1, textY + 1);

		g.setColor(Color.BLACK);
		g.drawString(text, textX, textY);

		g.dispose();
		return combined;
	}

	private Font findOptimalFont(Graphics2D g, String text, int maxWidth) {
		int fontSize = 48; // 初始较大字号
		Font font = new Font(DEFAULT_FONT, Font.BOLD, fontSize);
		FontMetrics metrics = g.getFontMetrics(font);

		while (metrics.stringWidth(text) > maxWidth && fontSize > 12) {
			fontSize--;
			font = new Font(font.getName(), font.getStyle(), fontSize);
			metrics = g.getFontMetrics(font);
		}

		// 使用抗锯齿字体
		return font.deriveFont(Font.BOLD, fontSize);
	}

	public String saveUltraQualityImage(BufferedImage image) {
		String localPath = "qrcode_premium_" + System.currentTimeMillis() + ".png";
		try {
			File outputFile = new File(localPath);

			// 使用PNG编码器保存最高质量
			ImageIO.write(image, "png", outputFile);

			log.info("超清二维码已保存到: {}", outputFile.getAbsolutePath());
			return localPath;
		} catch (IOException e) {
			throw new RuntimeException("保存二维码失败", e);
		}
	}
	/**
	 * 图像缩放工具方法
	 */
	private BufferedImage scaleImage(BufferedImage original, int width, int height) {
		BufferedImage scaled = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
		Graphics2D g = scaled.createGraphics();
		g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
				RenderingHints.VALUE_INTERPOLATION_BILINEAR);
		g.drawImage(original, 0, 0, width, height, null);
		g.dispose();
		return scaled;
	}

	/**
	 * 添加底部文字
	 */
	private BufferedImage addBottomText(BufferedImage qrImage, String text) {
		// 创建新图像(增加文字高度)
		BufferedImage combined = new BufferedImage(
				qrImage.getWidth(),
				qrImage.getHeight() + TEXT_HEIGHT,
				BufferedImage.TYPE_INT_ARGB
		);

		Graphics2D g = combined.createGraphics();

		// 绘制白色背景
		g.setColor(Color.WHITE);
		g.fillRect(0, 0, combined.getWidth(), combined.getHeight());

		// 绘制二维码
		g.drawImage(qrImage, 0, 0, null);

		// 设置文字样式
		g.setColor(Color.BLACK);
		g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
				RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

		// 自动调整字体大小
		Font font = findBestFitFont(g, text, qrImage.getWidth() - 40);
		g.setFont(font);

		// 计算文字位置
		FontMetrics metrics = g.getFontMetrics();
		int textX = (qrImage.getWidth() - metrics.stringWidth(text)) / 2;
		int textY = qrImage.getHeight() + (TEXT_HEIGHT + metrics.getAscent() - metrics.getDescent()) / 2;

		// 绘制文字
		g.drawString(text, textX, textY);
		g.dispose();

		return combined;
	}

	/**
	 * 自动调整字体大小以适应宽度
	 */
	private Font findBestFitFont(Graphics2D g, String text, int maxWidth) {
		int fontSize = 32; // 初始大小
		Font font = new Font(DEFAULT_FONT, Font.BOLD, fontSize);
		FontMetrics metrics = g.getFontMetrics(font);

		while (metrics.stringWidth(text) > maxWidth && fontSize > 8) {
			fontSize--;
			font = new Font(font.getName(), font.getStyle(), fontSize);
			metrics = g.getFontMetrics(font);
		}

		return font;
	}
}
