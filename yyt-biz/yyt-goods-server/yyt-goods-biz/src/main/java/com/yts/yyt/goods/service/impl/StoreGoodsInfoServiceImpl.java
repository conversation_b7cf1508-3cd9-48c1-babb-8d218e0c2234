package com.yts.yyt.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.admin.api.feign.RemoteDictService;
import com.yts.yyt.admin.api.utils.PageUtil;
import com.yts.yyt.common.core.constant.DelFlagConstants;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.core.util.ImageUtils;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.data.resolver.DictResolver;
import com.yts.yyt.common.data.resolver.ParamResolver;
import com.yts.yyt.common.es.EsService;
import com.yts.yyt.common.es.config.EsIndexEnum;
import com.yts.yyt.common.es.model.StoreGoodsModel;
import com.yts.yyt.common.file.service.ImageUrlService;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.tencent.service.TMSService;
import com.yts.yyt.common.tencent.vo.TextModerationVO;
import com.yts.yyt.goods.api.constants.GoodsConstant;
import com.yts.yyt.goods.api.constants.PublicLogConstant;
import com.yts.yyt.goods.api.dto.*;
import com.yts.yyt.goods.api.entity.*;
import com.yts.yyt.goods.api.enums.GoodsLotInfoEnum;
import com.yts.yyt.goods.api.enums.GoodsTypeEnum;
import com.yts.yyt.goods.api.enums.PublicLogTypeEnum;
import com.yts.yyt.goods.api.enums.StoreGoodsInfoEnum;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.goods.api.vo.*;
import com.yts.yyt.goods.config.AppraisalProperties;
import com.yts.yyt.goods.helper.ListTool;
import com.yts.yyt.goods.mapper.GoodsLotInfoMapper;
import com.yts.yyt.goods.mapper.StoreGoodsInfoMapper;
import com.yts.yyt.goods.mq.dto.StoreGoodsESMqDTO;
import com.yts.yyt.goods.service.*;
import com.yts.yyt.merchant.api.constant.Constant;
import com.yts.yyt.merchant.api.dto.BatchAddStoreGoodsDTO;
import com.yts.yyt.merchant.api.dto.CityPartnerAndMerchantDTO;
import com.yts.yyt.merchant.api.entity.CityPartner;
import com.yts.yyt.merchant.api.entity.IncentiveConfigEntity;
import com.yts.yyt.merchant.api.enums.DictEnum;
import com.yts.yyt.merchant.api.enums.IncentiveConfTypeEnum;
import com.yts.yyt.merchant.api.enums.MerchantShopTypeEnum;
import com.yts.yyt.merchant.api.exception.MerchantErrorEnum;
import com.yts.yyt.merchant.api.exception.MerchantException;
import jakarta.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.lucene.search.function.CombineFunction;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.elasticsearch.index.query.functionscore.RandomScoreFunctionBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service("storeGoodsInfoService")
public class StoreGoodsInfoServiceImpl extends ServiceImpl<StoreGoodsInfoMapper, StoreGoodsInfo> implements StoreGoodsInfoService {

    @Lazy
    @Autowired
    private BaseRemoteService baseRemoteService;
    
    @Autowired
    private SendSubmitAppraisalService sendSubmitAppraisalService;

    @Autowired
    private AppraisalProperties appraisalProperties;

	@Autowired
	private GoodsLotInfoService goodsLotInfoService;

	@Autowired
	private GoodsLotInfoMapper goodsLotInfoMapper;

    @Autowired
    private  RemoteDictService remoteDictService;

    @Autowired
    private TMSService tmsService;


    @Autowired
    private RedisTemplate<String, String> redisTemplate;

	@Autowired
	private GoodsFeedbackService goodsFeedbackService;

	@Resource
	private PublicLogService publicLogService;

	@Autowired
	private GoodsTraceabilityPreService goodsTraceabilityPreService;

    @Autowired
	@Lazy
    private GoodsAppraiseCallbackService goodsAppraiseCallbackService;

    @Autowired
    private StoreGoodsImageMd5Service storeGoodsImageMd5Service;

    @Autowired
    private ImageUrlService imageUrlService;
    @Autowired
    private GoodsImageInfoService goodsImageInfoService;

	@Autowired
	private EsService esService;

	@Autowired
	private GoodsAppraiseRecordService goodsAppraiseRecordService;
	@Autowired
	private TYjGoodsAgeService tYjGoodsAgeService;

	@Autowired
	private EnvAwareRocketMQTemplate envAwareRocketMQTemplate;

	@Autowired
	private GoodsCommissionRecordService goodsCommissionRecordService;

	@Lazy
	@Autowired
	private StoreGoodsInfoService storeGoodsInfoService;

    @Override
    @GlobalTransactional
    @Transactional(rollbackFor = Exception.class)
    public boolean add(AddStoreGoodsInfoDTO addParam) {
        log.info("商品添加，入参：{}",addParam);
        // 敏感词检测
        try {
            TextModerationVO result = tmsService.textModeration(addParam.getName(),
					addParam.getDescription(),addParam.getGoodsSize(),addParam.getPlaceOfOrigin());
            if (!result.isPassed()) {
                throw new GoodsException("文本包含敏感词：" + result.getSensitiveWords());
            }
        } catch (TencentCloudSDKException e) {
            log.error("敏感词检测服务异常", e);
            throw new GoodsException("敏感词检测服务异常");
        }
		// 构建藏品数据
		StoreGoodsInfo storeGoodsInfo = buildStoreGoods(addParam);
		// 保存商品图片信息
		goodsImageInfoService.batchAddByGoodsInfo(addParam.getMainImageList(), storeGoodsInfo.getId());
		//保存藏品
		return save(storeGoodsInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
		//1、只有待鉴定、鉴定失败状态的数据可以删除
		StoreGoodsInfo storeGoodsInfo = this.queryById(id);
		if (!StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode().equals(storeGoodsInfo.getState())
				&& !StoreGoodsInfoEnum.State.FAIL_APPRAISAL.getCode().equals(storeGoodsInfo.getState())) {
			throw GoodsException.build(GoodsBizErrorEnum.NOT_ALLOW_DELETE);
		}
		//2、如果是鉴定失败状态
		if (StoreGoodsInfoEnum.State.FAIL_APPRAISAL.getCode().equals(storeGoodsInfo.getState())) {
			// 2.1 DictEnum.CANNOT_DELETE_FAIL_GOODS_ROLE 字典配置的角色不允许删除
			if (isCannotDeleteRole()) {
				throw GoodsException.build(GoodsBizErrorEnum.CANNOT_DELETE_FAIL_GOODS_ROLE);
			}
			// 2.2 由于年代不合规导致状态为"鉴定失败"的藏品也不允许删除
			if (ObjUtil.isNotNull(storeGoodsInfo.getEraId())) {
				throw GoodsException.build(GoodsBizErrorEnum.NOT_ALLOW_DELETE_FAIL_GOODS);
			}
		}

        this.storeGoodsImageMd5Service.removeByGoodsId(id);
		//删除图片信息
		goodsImageInfoService.removeByGoodsId(id);
        return this.lambdaUpdate()
                .set(StoreGoodsInfo::getDelFlag, DelFlagConstants.DELETE)
				.set(StoreGoodsInfo::getUpdateTime, LocalDateTime.now())
                .eq(StoreGoodsInfo::getId, id)
                .update();
    }

    @Override
    @GlobalTransactional
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(AddStoreGoodsInfoDTO updateParam) {
        // 敏感词检测
        try {
            TextModerationVO result = tmsService.textModeration(updateParam.getName(),
					updateParam.getDescription(),updateParam.getGoodsSize(),updateParam.getPlaceOfOrigin());
            if (!result.isPassed()) {
                throw new GoodsException("文本包含敏感词：" + result.getSensitiveWords());
            }
        } catch (TencentCloudSDKException e) {
            log.error("敏感词检测服务异常", e);
            throw new GoodsException("敏感词检测服务异常");
        }

        StoreGoodsInfo update = new StoreGoodsInfo();
        boolean flag = false;
        StoreGoodsInfo storeGoodsInfo = getById(updateParam.getId());
        // 待鉴定-修改全部
        if (ObjUtil.isNotNull(storeGoodsInfo) &&
                storeGoodsInfo.getState().equals(StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode())) {
            BeanUtils.copyProperties(updateParam, update);
            update.setUpdateTime(LocalDateTime.now());
            update.setMainImage(ImageUtils.getFirstImageUrl(JSONUtil.toJsonStr(updateParam.getMainImageList())));
            //处理图片信息
            goodsImageInfoService.removeByGoodsId(update.getId());
            goodsImageInfoService.batchAddByGoodsInfo(updateParam.getMainImageList(), update.getId());
            
            update.setCurrentPrice(updateParam.getSalePrice());
            update.setGoodsNo(updateParam.getGoodsNo());
            flag = updateById(update);
        } else if (ObjUtil.isNotNull(storeGoodsInfo) // 待公示、已公示-只允许修改 名称、价格、描述
                && (storeGoodsInfo.getState().equals(StoreGoodsInfoEnum.State.PRE_PUBLICITY.getCode())
                || storeGoodsInfo.getState().equals(StoreGoodsInfoEnum.State.COMPLETED.getCode())
        )
        ) {
            storeGoodsInfo.setCurrentPrice(updateParam.getSalePrice());
            storeGoodsInfo.setSalePrice(updateParam.getSalePrice());
            storeGoodsInfo.setName(updateParam.getName());
            storeGoodsInfo.setDescription(updateParam.getDescription());
            flag = updateById(storeGoodsInfo);
            // 同步修改下面的所有拍品数据 名称、价格
			goodsLotInfoService.updateInfoByGoodsId(updateParam);
            GoodsLotInfo goodsLotInfo =
                    goodsLotInfoService.lambdaQuery()
                            .eq(GoodsLotInfo::getGoodsId, storeGoodsInfo.getId())
                            .orderByDesc(GoodsLotInfo::getCreateTime)
                            .last(" limit 1")
                            .one();
            // 已公示数据，重新调用上链
            if (storeGoodsInfo.getState().equals(StoreGoodsInfoEnum.State.COMPLETED.getCode()) &&
                    ObjUtil.isNotNull(goodsLotInfo) &&
                    (goodsLotInfo.getState().equals(GoodsLotInfoEnum.State.WAIT_SELLING.getCode()) ||
                            goodsLotInfo.getState().equals(GoodsLotInfoEnum.State.SELLING.getCode()))) {
                // 查询商品的图片信息
                List<ImageUrlDTO> imageUrls = goodsImageInfoService.getImageUrlByGoodsId(storeGoodsInfo.getId());
                storeGoodsInfo.setMainImage(JSONUtil.toJsonStr(imageUrls));
                goodsTraceabilityPreService.addChainAndQRCode(getChainData(storeGoodsInfo));
            }
        } else {
            throw new GoodsException("该状态藏品不允许修改");
        }
        return flag;
    }

	/**
	 * 管理后台-取消公示
	 * @param dto id
	 * @return res
	 */
	@Override
	public Boolean cancelPublicity(IdDTO dto) {
		if (IdDTO.idAndIdsIsEmpty(dto)) {
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		GoodsCancelPublicityDTO cancelPublicityDTO = new GoodsCancelPublicityDTO();
		cancelPublicityDTO.setId(Long.valueOf(dto.getId()));
		cancelPublicityDTO.setRemark(PublicLogConstant.CANCEL_PUBLIC);
		return cancelPublicity(cancelPublicityDTO);
	}

	/**
	 * 取消公示
	 * @param dto
	 * @return res
	 */
	@Override
	public Boolean cancelPublicity(GoodsCancelPublicityDTO dto) {
		StoreGoodsInfo dbRecord = queryById(dto.getId());
		if (!StoreGoodsInfoEnum.State.PUBLICITY.getCode().equals(dbRecord.getState())) {
			return Boolean.FALSE;
		}
		// 取消公示, 保存藏品公示记录, 状态转换: 公示中 -> 待公示
		processPublicLog(Collections.singletonList(dto.getId()), PublicLogTypeEnum.WAIT_PUBLIC.getCode(),dto.getRemark());
		// 从es删除藏品
		sendGoods2ESMsg(Collections.singletonList(dto.getId()));
		// 修改藏品记录：待公示
		return new LambdaUpdateChainWrapper<>(this.baseMapper).eq(StoreGoodsInfo::getId, dto.getId())
				.set(StoreGoodsInfo::getState, StoreGoodsInfoEnum.State.PRE_PUBLICITY.getCode())
				.update();
	}

	private StoreGoodsInfo queryById(Long id) {
		//查询拍品
		StoreGoodsInfo goods = this.getById(id);
		if (ObjUtil.isNull(goods)) {
			throw GoodsException.build(GoodsBizErrorEnum.DATA_NOT_EXIST);
		}
		return goods;
	}

	/**
	 * 保存公示记录
	 * @param ids 藏品ids
	 * @param logType 日志类型
	 * @param remark 备注
	 */
	private void processPublicLog(List<Long> ids,String logType, String remark) {
		// 取消公示, 保存藏品公示记录, 状态转换: 公示中 -> 待公示
		PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
		// 修改藏品状态
		publicLogSaveDTO.setGoodsIds(ids)
				.setPublicLogType(logType)
				.setRemark(StrUtil.isBlank(remark) ? PublicLogConstant.UPDATE_GOODS_INFO : remark);
		publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
    }

    @Override
	public Page<StoreGoodsInfoVO> getBasePage(Page pageParam, SearchStoreGoodsInfoDTO search) {
		//如果不是管理员等角色，则只能查看相关商户的藏品数据
		if(!getSelectRoleData()){
			//根据登录用户查询商户信息
			List<Long> merchantIds = baseRemoteService.getMerchantIdsBySysUserId(SecurityUtils.getUser().getId());
			log.info("【getBasePage】根据系统用户id查询商户信息：{}",JSONObject.toJSONString(merchantIds));
			if (CollUtil.isEmpty(merchantIds)) {
				log.info("【getBasePage】未查询到相关商户信息");
				return new Page<>();
			}
			search.setMerchantIds(merchantIds);
		}

		Page<StoreGoodsInfoVO> page = this.baseMapper.getBasePage(pageParam, search);
		//信息处理
		if (CollUtil.isNotEmpty(page.getRecords())) {
			List<Long> goodsIds = page.getRecords().stream().map(StoreGoodsInfoVO::getId).collect(Collectors.toList());
			Map<Long, List<ImageUrlDTO>> imageMap = goodsImageInfoService.getImageUrlMapByGoodsIds(goodsIds);
			for (int i = 0; i < page.getRecords().size(); i++) {
				StoreGoodsInfoVO vo = page.getRecords().get(i);
				vo.setShopTypeName(MerchantShopTypeEnum.getByCode(vo.getShopType()+"").getDesc());
				vo.setImgUrl(imageUrlService.convertImageUrl(vo.getMainImage()).getMediumUrl());
				vo.setMainImage(imageUrlService.convertImageUrl(vo.getMainImage()).getOriginUrl());
				vo.setMainImageList(imageMap.get(vo.getId()));
				// 店铺头像
				vo.setShopAvatar(imageUrlService.convertImageUrl(vo.getShopAvatar()).getSmallUrl());
			}
		}
		return page;
	}

    /**
     * 查询配置字典，是否允许查询全部藏品数据
     */
	@Override
    public Boolean getSelectRoleData() {
        boolean selectBool = false;
        R<List<SysDictItem>> dict =
                remoteDictService.getInnerDictByType(DictEnum.SELECT_GOODS_ROLE.getCode());
        List<Long> roleIds = SecurityUtils.getRoleIds();
        List<SysDictItem> dictItemList = dict.getData();
        for (int i = 0; i < dictItemList.size(); i++) {
            for (int j = 0; j < roleIds.size(); j++) {
                if (roleIds.get(j).equals(Long.valueOf(dictItemList.get(i).getItemValue()))) {
                    selectBool = true;
                    break;
                }
            }
        }
        return selectBool;
    }

    /**
     * 查询配置字典，是否允许查询全部藏品数据
     */
	//是否允许删除
	public Boolean isCannotDeleteRole() {
		R<List<SysDictItem>> cannotDeleteRoleR = remoteDictService.getInnerDictByType(DictEnum.CANNOT_DELETE_FAIL_GOODS_ROLE.getCode());
		if (null == cannotDeleteRoleR || CollUtil.isEmpty(cannotDeleteRoleR.getData())) {
			return Boolean.FALSE;
		}
		List<Long> roleIds = SecurityUtils.getRoleIds();
		Set<Long> cannotDeleteRoles = cannotDeleteRoleR.getData().stream()
				.map(item -> Long.valueOf(item.getItemValue()))
				.collect(Collectors.toSet());

		return roleIds.stream().anyMatch(cannotDeleteRoles::contains);
	}

    @Override
    public StoreGoodsInfoVO getVOById(Long id) {
        StoreGoodsInfoVO vo = this.baseMapper.getVOById(id);
		// 查询上链信息
		vo.setTraceCode(goodsTraceabilityPreService.getTraceCodeByGoodsNo(vo.getGoodsNo()));
		if (ObjUtil.isNull(vo)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_NOT_EXIST);
		}
        vo.setUpperChainInfo(vo.getTraceCode());
		// 商户类型处理
		vo.setShopTypeName(MerchantShopTypeEnum.getByCode(vo.getShopType()+"").getDesc());
		// 店铺头像
		vo.setShopAvatar(imageUrlService.convertImageUrl(vo.getShopAvatar()).getSmallUrl());
		// 主图处理
		vo.setImgUrl(imageUrlService.convertImageUrl(vo.getMainImage()).getMediumUrl());
		// 查询商品的图片信息
		List<ImageUrlDTO> images = goodsImageInfoService.getImageUrlByGoodsId(id);
		List<ImageUrlDTO> originUrlImagesList = new ArrayList<>();
		for (ImageUrlDTO image : images) {
			String imgUrl = image.getUrl();
			image.setUrl(imageUrlService.convertImageUrl(imgUrl).getMediumUrl());
			ImageUrlDTO imageUrlDTO = new ImageUrlDTO();
			imageUrlDTO.setUrl(imgUrl);
			originUrlImagesList.add(imageUrlDTO);
		}
		vo.setMainImageList(images);
		vo.setOriginImageUrl(originUrlImagesList);
        return vo;
    }


    @Override
    @Transactional
    public Boolean submitAppraisal(StoreUpdateStateDTO batchUpdateState) {

        List<StoreGoodsInfo> list = this.lambdaQuery()
                .in(StoreGoodsInfo::getId, batchUpdateState.getIds()).list();
        if (list.size() > 0) {
			// 判断是否存在自营藏品
			String selfPartnerId = DictResolver.getDictItemValue(Constant.SELF_SUPPORT_PARTNER_DICT_KEY, Constant.SELF_PARTNER_ID);
			if (StrUtil.isNotBlank(selfPartnerId)) {
				Long partnerId = Long.valueOf(selfPartnerId);
                if (list.stream().anyMatch(storeGoodsInfo -> storeGoodsInfo.getCityPartnerId().equals(partnerId))) {
					throw GoodsException.build(GoodsBizErrorEnum.SELF_SUPPORT_GOODS_NOT_SUBMIT_APPRAISAL);
                }
			}
			// 查询图片信息
			Map<Long, List<ImageUrlDTO>> imageMap = goodsImageInfoService.getImageUrlMapByGoodsIds(batchUpdateState.getIds());
			for (StoreGoodsInfo storeGoodsInfo : list) {
				if (imageMap.containsKey(storeGoodsInfo.getId())) {
					storeGoodsInfo.setMainImage(JSONUtil.toJsonStr(imageMap.get(storeGoodsInfo.getId())));
				}
			}
			// 提交鉴定
            List<AppraisalRetnVo> appraisalRetnVoList =
                    sendSubmitAppraisalService.submitAppraisal(list);
            // 返回处理，三方鉴定单号，鉴定状态
            for (AppraisalRetnVo vo : appraisalRetnVoList) {
                this.lambdaUpdate()
                        .set(StoreGoodsInfo::getState, StoreGoodsInfoEnum.State.WAIT_APPRAISAL.getCode())
                        .set(StoreGoodsInfo::getAppraisalNo,vo.getAppraisalNo())
                        .eq(StoreGoodsInfo::getId,vo.getOutAppraisalId())
                        .update();
            }
        }
        
        return true;
    }

    @Override
	@GlobalTransactional
    @Transactional(rollbackFor = Exception.class)
    public cn.hutool.json.JSONObject yujianCallback(Map<String, Object> requestBody) {
        log.info("域鉴鉴定回调处理,开始---->>>参数:{}" , JSON.toJSONString(requestBody));
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        String callbackSrc = String.valueOf(requestBody.get("src"));
        String key = appraisalProperties.getKey();
        String timestamp = String.valueOf(requestBody.get("timestamp"));
        String md5 = String.valueOf(requestBody.get("md5"));
        if(!sendSubmitAppraisalService.getMd5Data(timestamp,callbackSrc,key).equals(md5)){
            jsonObject.accumulate("retn","-1");
            jsonObject.accumulate("desc","md5校验不通过");
            return jsonObject;
        }
        // 同步数据到数据库
        goodsAppraiseCallbackService.saveCallbackData(JSONUtil.toJsonStr(requestBody.get("msg")));
        jsonObject.accumulate("retn","0");
        jsonObject.accumulate("desc","操作成功");
        log.info("域鉴鉴定回调处理,结束");
        return jsonObject;
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public cn.hutool.json.JSONObject yujianReplyCallback(Map<String, Object> requestBody) {

		log.info("域鉴专家回复鉴定回调处理,开始---->>>参数:{}" , JSON.toJSONString(requestBody));
		cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
		String callbackSrc = String.valueOf(requestBody.get("src"));
		String key = appraisalProperties.getKey();
		String timestamp = String.valueOf(requestBody.get("timestamp"));
		String md5 = String.valueOf(requestBody.get("md5"));
		if(!sendSubmitAppraisalService.getMd5Data(timestamp,callbackSrc,key).equals(md5)){
			jsonObject.accumulate("retn","-1");
			jsonObject.accumulate("desc","md5校验不通过");
			return jsonObject;
		}

		JSONObject msg = new JSONObject((Map<String, Object>) requestBody.get("msg"));
		List<AppraisalResVo> replyList = msg.getJSONArray("replyList").toJavaList(AppraisalResVo.class);
		if (replyList == null || replyList.isEmpty()) {
			jsonObject.accumulate("retn","0");
			jsonObject.accumulate("desc","操作成功");
			log.info("域鉴鉴定回调处理,结束");
			return jsonObject;
		}

		// 转换为goodsId列表
		Set<Long> goodIds = replyList
				.stream()
				.map(AppraisalResVo::getOutAppraisalId)
				.map(Long::valueOf)
				.collect(Collectors.toSet());

		// 查询已有的鉴定记录并分组
		Map<String, Set<String>> recordGroupMap = goodsAppraiseRecordService.listByIds(goodIds)
				.stream()
				.collect(
						Collectors.groupingBy(
								// 先根据goodsId分组
								record -> String.valueOf(record.getGoodsId()),
								// 再转换为专家编号set集合
								Collectors.mapping(
										GoodsAppraiseRecord::getAppraisalExpertNum,
										Collectors.toSet()
								)
						)
				);

		// 过滤已存在的，并且转换为GoodsAppraiseRecord
		List<GoodsAppraiseRecord> appraiseRecordList = replyList.stream()
				.filter(res -> !recordGroupMap.getOrDefault(res.getOutAppraisalId(), Collections.emptySet()).contains(res.getAppraisalExpertNum()))
				.map(res -> new GoodsAppraiseRecord()
						.setId(IDS.uniqueID())
						.setGoodsId(Long.valueOf(res.getOutAppraisalId()))
						.setAppraisalEra(res.getAppraisalEra())
						.setAppraisalGuanWare(res.getAppraisalGuanWare())
						.setAppraiseTime(res.getAppraisalTime())
						.setAppraisePrice(res.getAppraisePrice())
						.setAppraisalExpertNum(res.getAppraisalExpertNum()))
				.toList();

		// 存储鉴定记录到数据库
		if (!appraiseRecordList.isEmpty()) {
			goodsAppraiseRecordService.saveBatch(appraiseRecordList);
		}

		jsonObject.accumulate("retn","0");
		jsonObject.accumulate("desc","操作成功");
		log.info("域鉴鉴定回调处理,结束");
		return jsonObject;
	}

	@Override
    public StoreGoodsInfoFeignVO getInfo(String goodNo) {
        StoreGoodsInfo storeGoodsInfo = this.lambdaQuery()
                .eq(StoreGoodsInfo::getGoodsNo, goodNo)
                .one();
        StoreGoodsInfoFeignVO storeGoodsInfoFeignVO = new StoreGoodsInfoFeignVO();
        BeanUtils.copyProperties(storeGoodsInfo, storeGoodsInfoFeignVO);
        // 商品名称取拍品表商品名称，
        GoodsLotInfo goodsLotInfo =
                goodsLotInfoService.lambdaQuery()
                        .eq(GoodsLotInfo::getGoodsId, storeGoodsInfo.getId())
                        .orderByDesc(GoodsLotInfo::getCreateTime)
                        .last(" limit 1")
                        .one();
        if (ObjUtil.isNotNull(goodsLotInfo)) {
			storeGoodsInfoFeignVO.setName(goodsLotInfo.getName());
		}
        return storeGoodsInfoFeignVO;
    }

    @Override
    public List<CommissionLadderCountVO> storeGoodsInfoForIncentivePartner(CommissionQueryDTO dto){
        return baseMapper.storeGoodsInfoForIncentivePartner(dto);
    }

    @Override
    public long countAppraisalSuccessGoodsByTimeRange(StoreGoodsInfoIncentiveCountDTO dto){
        Long goodsCount = baseMapper.selectCount(
                Wrappers.lambdaQuery(StoreGoodsInfo.class)
                        .eq(StoreGoodsInfo::getDelFlag, DelFlagConstants.NORMAL)
                        .eq(StoreGoodsInfo::getAppraisalState, StoreGoodsInfoEnum.AppraisalState.APPRAISAL_SUCCESS.getCode())
                        .eq(dto.getPartnerId() != null, StoreGoodsInfo::getCityPartnerId, dto.getPartnerId())
                        .ge(StrUtil.isNotBlank(dto.getStartTime()),StoreGoodsInfo::getAppraisalTime, dto.getStartTime())
                        .lt(StrUtil.isNotBlank(dto.getEndTime()),StoreGoodsInfo::getAppraisalTime, dto.getEndTime())
        );

        return goodsCount != null ? goodsCount : 0;
    }

    /**
     * 构建商品实体
     * @param addParam param
     * @return StoreGoodsInfo
     */
    private StoreGoodsInfo buildStoreGoods(AddStoreGoodsInfoDTO addParam) {
		if(ObjectUtil.isEmpty(addParam.getMerchantId()) || ObjectUtil.isEmpty(addParam.getMainImageList())) {
			throw new GoodsException(GoodsBizErrorEnum.PARAMS_ERROR);
		}
        StoreGoodsInfo add = new StoreGoodsInfo();
        BeanUtils.copyProperties(addParam, add);
        add.setId(IDS.uniqueID());
        add.setAppraisalState(StoreGoodsInfoEnum.AppraisalState.PRE_APPRAISAL.getCode());
        add.setState(StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode());
        add.setDelFlag(DelFlagConstants.NORMAL);
		add.setMainImage(ImageUtils.getFirstImageUrl(JSONUtil.toJsonStr(addParam.getMainImageList())));
        add.setMerchantId(add.getMerchantId());
        add.setCurrentPrice(addParam.getSalePrice());
        CityPartnerAndMerchantDTO merchantAndPartnerEntity = baseRemoteService.getPartnerByMerchantId(add.getMerchantId());
        CityPartner cityPartner = merchantAndPartnerEntity.getCityPartner();
        add.setCityPartnerId(cityPartner.getId());
        add.setGoodsNo(generateGoodsNo(cityPartner.getPartnerNo()));
        return add;
    }

	/**
     * 转换链数据
     * @param goodsInfo
     * @return
     */
    private AddChainAndQRCodeDTO getChainData(StoreGoodsInfo goodsInfo) {
        AddChainAndQRCodeDTO dto = new AddChainAndQRCodeDTO();
        dto.setName(goodsInfo.getName());
        dto.setGoodsEra(goodsInfo.getGoodsEra());
        dto.setGoodsCondition(goodsInfo.getGoodsCondition());
        dto.setGoodsSize(goodsInfo.getGoodsSize());
        dto.setSalePrice(goodsInfo.getSalePrice());
        dto.setStockQuantity(1);
		dto.setMainImage(goodsInfo.getMainImage());
//        dto.setDetailInfo(goodsInfo.getDetailInfo());
        dto.setPlaceOfOrigin(goodsInfo.getPlaceOfOrigin());
        dto.setOwnership("");
        dto.setGoodsNo(goodsInfo.getGoodsNo());
		dto.setType(GoodsTypeEnum.TYPE_SHOP.getType());
        return dto;
    }


    /**
     * 生成商品编号
     * 规则：合伙人ID + YYYYMMDD + 6位数字
     * 6位数字每天都从000001向上递增
     * @param partnerId 合伙人ID
     * @return 商品编号
     */
    private String generateGoodsNo(Integer partnerId) {
        // 获取当前日期
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 从Redis获取当日序号
        String key = "goods:no:" + partnerId + ":" + dateStr;
        Long sequence = redisTemplate.opsForValue().increment(key);

        // 如果key不存在，设置过期时间为24小时
        if (sequence == 1) {
            redisTemplate.expire(key, 24, TimeUnit.HOURS);
        }

        // 生成6位序号，不足6位前面补0
        String sequenceStr = String.format("%06d", sequence);

        // 拼接商品编号：合伙人ID + 日期 + 6位序号
        return partnerId + dateStr + sequenceStr;
    }


	@Override
	public List<StoreGoodsInfo> getPublicityEndList(){
		return new LambdaQueryChainWrapper<>(this.baseMapper)
				.eq(StoreGoodsInfo::getState, StoreGoodsInfoEnum.State.PUBLICITY.getCode())
				.le(StoreGoodsInfo::getPublicityEndTime, LocalDateTime.now())
				.list();
	}


	/**
	 * 公示到期，自动上架
	 * @param storeGoodsInfo 商品信息
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void publicityToSelling(StoreGoodsInfo storeGoodsInfo) {
		//1、修改藏品公示状态为：已公示
		new LambdaUpdateChainWrapper<>(this.baseMapper)
				.set(StoreGoodsInfo::getState, StoreGoodsInfoEnum.State.COMPLETED.getCode())
				.eq(StoreGoodsInfo::getId, storeGoodsInfo.getId())
				.update();
		//2、需要自动生成一个拍品记录
		goodsLotInfoService.saveByStoreGoodsInfo(storeGoodsInfo.getId());

		//3、保存藏品公示记录, 状态转换: 公示中 -> 已公示
		processPublicLog(Collections.singletonList(storeGoodsInfo.getId()), PublicLogTypeEnum.PUBLIC_COMPLETED.getCode(), PublicLogConstant.PUBLIC_COMPLETED);
		//4、上链
		// 查询商品的图片信息
		List<ImageUrlDTO> imageUrls = goodsImageInfoService.getImageUrlByGoodsId(storeGoodsInfo.getId());
		storeGoodsInfo.setMainImage(JSONUtil.toJsonStr(imageUrls));
		goodsTraceabilityPreService.addChainAndQRCode(getChainData(storeGoodsInfo));
		//5、保存佣金记录
		// goodsCommissionRecordService.addRecord(storeGoodsInfo);
		// 删除公示结束的藏品
		sendGoods2ESMsg(Collections.singletonList(storeGoodsInfo.getId()));
	}

	/**
	 * 批量公示
	 * @param param 商品ids
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchPublicity(GoodsPublicityDTO param) {
		log.info("【batchPublicity】批量公示，入参：{}",param);

		//1、查询商品信息
		List<StoreGoodsInfo> dbList = new LambdaQueryChainWrapper<>(this.baseMapper)
				.in(StoreGoodsInfo::getState, CollUtil.newArrayList(StoreGoodsInfoEnum.State.PRE_PUBLICITY.getCode(), StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode()))
				.in(StoreGoodsInfo::getId, param.getGoodsIds())
				.list();
		if (CollUtil.isEmpty(dbList)) {
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_CANNOT_EDIT_TO_PUBLICITY_ERROR);
		}
		// 校验是否存在未处理的举报公示数据
		List<GoodsFeedbackEntity> checkList = goodsFeedbackService.bacthValidByGoodsId(dbList.stream().map(StoreGoodsInfo::getId).toList());
		if (CollUtil.isNotEmpty(checkList)) {
			List<Long> checkIds = checkList.stream().map(GoodsFeedbackEntity::getGoodsId).toList();
			String nameJoin = StrUtil.join(",", new LambdaQueryChainWrapper<>(this.baseMapper)
					.select(StoreGoodsInfo::getName)
					.in(StoreGoodsInfo::getId, checkIds)
					.list().stream().map(StoreGoodsInfo::getName).toList());
			throw new GoodsException(nameJoin+"-存在被举报记录");
		}
		//2、分批，>50 <=50  2025-04-25需求：固定公示周期2天
//		List<StoreGoodsInfo> underList = Lists.newArrayList(); // 小于等于50
//		List<StoreGoodsInfo> aboveList = Lists.newArrayList(); // 大于50
//		for (StoreGoodsInfo goodsInfo : dbList) {
//			if (goodsInfo.getSalePrice().compareTo(GoodsConstant.publicity_amount) > 0) {
//				aboveList.add(goodsInfo);
//			}else {
//				underList.add(goodsInfo);
//			}
//		}
		// 处理鉴定信息
		processAppraisalInfo(dbList);
		//3、计算并更新公示信息
		updatePublicityInfo(dbList);

	}

	/**
	 * 更新商品公示信息
	 * @param list 商品信息
	 */
	private void updatePublicityInfo(List<StoreGoodsInfo> list) {
        LocalDateTime now = LocalDateTime.now();
		StoreGoodsInfo first = list.get(0);
		calculatePublicityTime(first, now, null);
		log.info("【updatePublicityInfo】公示日期计算结束：{}-{}",first.getPublicityTime(),first.getPublicityEndTime());
		//更新数据
		List<Long> ids = list.stream().map(StoreGoodsInfo::getId).toList();
		new LambdaUpdateChainWrapper<>(this.baseMapper).set(StoreGoodsInfo::getPublicityTime, first.getPublicityTime())
				.set(StoreGoodsInfo::getPublicityStartTime, first.getPublicityStartTime())
				.set(StoreGoodsInfo::getPublicityEndTime, first.getPublicityEndTime())
				.set(StoreGoodsInfo::getState, StoreGoodsInfoEnum.State.PUBLICITY.getCode())
				.in(StoreGoodsInfo::getId, ids)
				.update();

		// 保存藏品公示记录, 状态转换: 待公示 -> 公示中
		PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
		publicLogSaveDTO.setGoodsIds(ids)
				.setPublicityTime(first.getPublicityTime() + "")
				.setPublicityStartTime(first.getPublicityStartTime())
				.setPublicityEndTime(first.getPublicityEndTime())
				.setPublicLogType(PublicLogTypeEnum.PUBLIC.getCode())
				.setRemark(PublicLogConstant.SELECT_PUBLIC);
		publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);
		// 添加公示藏品到es
		sendGoods2ESMsg(ids);
	}

	private void sendGoods2ESMsg(List<Long> ids) {
		log.info("发送同步goods lot藏品到es消息，id列表：{}", ids);
		storeGoodsInfoService.syncStoreGoods2ES(ids);
		log.info("发送同步goods lot藏品到es消息结束");


//
//		log.info("发送同步store goods藏品到es消息，id列表：{}", ids);
//		if (ids.isEmpty()) {
//			return;
//		}
//
//		// 构建消息体
//		StoreGoodsESMqDTO dto = new StoreGoodsESMqDTO(ids);
//		Message<StoreGoodsESMqDTO> message = MessageBuilder
//				.withPayload(dto)
//				.build();
//		SendReceipt sendReceipt = envAwareRocketMQTemplate.sendDelayMsgWithTag(
//                "rocketMQClientTemplate",
//				RocketMQConstants.Topic.TOPIC_GOODS_ES_GRANT,
//				RocketMQConstants.Tag.TAG_STORE_GOODS_ES_GRANT,
//				message,
//				Duration.ofSeconds(1)
//		);
//		log.info("发送同步store goods藏品到es消息成功，message id: {}", sendReceipt.getMessageId());
		// 构建消息体
	}

    public void processAppraisalInfo(List<StoreGoodsInfo> list) {
        List<StoreGoodsInfo> updateAppraisalList = list.stream()
                .filter(goods -> goods.getState().equals(StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode())).toList();
        if (CollUtil.isNotEmpty(updateAppraisalList)) {
            log.info("【processAppraisalInfo】处理鉴定信息：{}",JSONObject.toJSONString(updateAppraisalList));
            // 查询藏品等级配置信息
            List<IncentiveConfigEntity> baseIncentiveConfigLs = baseRemoteService.getIncentiveConfigLs(IncentiveConfTypeEnum.CONF_TYPE1);
            for (StoreGoodsInfo goods : updateAppraisalList) {
                goods.setAppraisalState(StoreGoodsInfoEnum.AppraisalState.APPRAISAL_SUCCESS.getCode());
                goods.setAppraisalTime(goods.getCreateTime());
                goods.setAppraisePrice(goods.getSalePrice());
                goods.setAppraisalAuthenticity("0");  //0-真、1-假
                //处理鉴定等级
                for (IncentiveConfigEntity conf : baseIncentiveConfigLs){
                    if (goods.getSalePrice().compareTo(new BigDecimal(conf.getMaxVal())) <= 0
                            && goods.getSalePrice().compareTo(new BigDecimal(conf.getMinVal())) >= 0) {
                        goods.setAppraisalLevel(conf.getConfTitle());
                        break;
                    }
                }
                // 处理鉴定年代
                Integer appraisalEraId = tYjGoodsAgeService.getEraIdByName(goods.getGoodsEra());
                if(appraisalEraId == null){
                    log.info("藏品年代不存在，藏品id：{}, 年代：{}",  goods.getId(), goods.getGoodsEra());
                    throw GoodsException.build(GoodsBizErrorEnum.NOT_EXIST_APPRAISE_AGE);
                }
                goods.setEraId(appraisalEraId);
            }
            // 更新鉴定信息
            updateBatchById(updateAppraisalList);
        }
    }

	/**
	 * 计算公示时间：工作日（不计算周末）
	 * @param add goods记录
	 * @param now 起始日期
	 * @param putOffDays 顺延日期
	 */
	public static void calculatePublicityTime(StoreGoodsInfo add, LocalDateTime now, Integer putOffDays) {
		//获取公示期
		Integer period = getPeriodFromConfig(add.getSalePrice());
		if (ObjUtil.isNotNull(putOffDays)) {
			period = period + putOffDays;
		}
		LocalDate lastDay = now.toLocalDate();
		//计算公示结束时间（不算周末）
//		for (int i = 0; i < period; i++) {
//			//当前日期+1天，是否是周末，是：则继续+1，否则进入下一个循环
//			lastDay = checkDateIsWeek(lastDay);
//		}
		// TODO: 2025/5/1 不需要跳过周末
		lastDay = lastDay.plusDays(period);
		add.setPublicityTime(period);
		add.setPublicityStartTime(now.toLocalDate().plusDays(1).atTime(0,0,0));
		add.setPublicityEndTime(lastDay.atTime(23,59,59));
	}

	public static LocalDate checkDateIsWeek(LocalDate today) {
		//当前日期+1天，是否是周末，是：则继续+1，否则进入下一个循环
		LocalDate nextDay = today.plusDays(1);
		DayOfWeek dayOfWeek = nextDay.getDayOfWeek();
		if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
			log.info("{}-不是周末",nextDay);
			return nextDay;
		}
		log.info("{}-是周末",nextDay);
		return checkDateIsWeek(nextDay);
	}

	private static Integer getPeriodFromConfig(BigDecimal salePrice) {
		//获取公示周期
		String period = "";
//		if (salePrice.compareTo(GoodsConstant.publicity_amount) > 0) {
//			period = StrUtil.blankToDefault(DictResolver.getDictItemValue(GoodsConstant.publicity_period, GoodsConstant.publicity_period_fifty_above),GoodsConstant.publicity_15);
//		}else {
//			period = StrUtil.blankToDefault(DictResolver.getDictItemValue(GoodsConstant.publicity_period, GoodsConstant.publicity_period_fifty_under),GoodsConstant.publicity_7);
//		}
		//2025-04-24，公示周期固定：2天
		period = StrUtil.blankToDefault(DictResolver.getDictItemValue(GoodsConstant.publicity_period, GoodsConstant.publicity_period_fix),GoodsConstant.publicity_2);
		return Integer.valueOf(period);
	}

	/**
	 * 公示时间顺延
	 * @param id 商品id
	 * @param putOffDays 顺延天数
	 */
	@Override
	public void calculatePublicityTimeWithPutOff(@NonNull Long id, @NonNull Integer putOffDays) {
		StoreGoodsInfo byId = queryById(id);

		if (ObjUtil.isNotNull(byId.getPublicityEndTime())) {
//			calculatePublicityTime(byId, byId.getPublicityEndTime(), putOffDays);
			calculatePublicityTime(byId, LocalDateTime.now(), putOffDays);
			//更新
			byId.setState(StoreGoodsInfoEnum.State.PUBLICITY.getCode());
			this.updateById(byId);

			// 保存藏品公示日志, 状态转换: 待公示 -> 公示中
			PublicLogSaveDTO publicLogSaveDTO = new PublicLogSaveDTO();
			publicLogSaveDTO.setGoodsIds(Collections.singletonList(byId.getId()))
					.setPublicityTime(byId.getPublicityTime() + "")
					.setPublicityStartTime(byId.getPublicityStartTime())
					.setPublicityEndTime(byId.getPublicityEndTime())
					.setPublicLogType(PublicLogTypeEnum.PUBLIC.getCode())
					.setRemark(PublicLogConstant.AUDIT_FALSE);
			publicLogService.saveOrUpdatePublicLog(publicLogSaveDTO);

			// 同步到es
			sendGoods2ESMsg(Collections.singletonList(id));
		}
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchAddStoreGoods(BatchAddStoreGoodsDTO batchAddParam) {
        List<String> errorList = new ArrayList<>();
        CityPartnerAndMerchantDTO cityPartnerAndMerchantDTO = baseRemoteService.getPartnerByMerchantId(Long.parseLong(batchAddParam.getMerchantId()));
        // 校验商户是否存在 & 判断城市合伙人角色
        Long sysId = SecurityUtils.getUser().getId();
        if(cityPartnerAndMerchantDTO.getMerchant() == null || !sysId.equals(cityPartnerAndMerchantDTO.getCityPartner().getSysUserId())) {
            throw MerchantException.build(MerchantErrorEnum.PARAMS_ERROR);
        }
        // 设置商品编号
        List<AddStoreGoodsDTO> storeGoods = batchAddParam.getStoreGoods();
        // 计算本次批量新增的重复商品编号集合
        List<String> duplicateGoodsNos = getDuplicateGoodsNos(storeGoods);
        // 批量新增
        List<StoreGoodsInfo> addList = new ArrayList<>();
        List<StoreGoodsImageMd5Entity> imageMd5s = new ArrayList<>();
        List<GoodsImageInfo> goodsImageInfoList = new ArrayList<>();
		//查询年代
		Map<String, BigDecimal> failEraMap = DictResolver.getDictItemsByType(GoodsConstant.GOODS_FAIL_AGE)
				.stream().collect(Collectors.toMap(SysDictItem::getLabel, item->new BigDecimal(item.getDescription())));
        // 添加开关校验
        // 首图校验开关
        String switchFlag = ParamResolver.getStr("STORE_GOODS_REPEAT_CHECK","false");
        for (AddStoreGoodsDTO goods : storeGoods) {
            // 如果商品编号在重复列表中,跳过该商品
            if (Boolean.valueOf(switchFlag) && duplicateGoodsNos.contains(goods.getGoodsNumber())) {
                errorList.add("商品编号为：" + goods.getGoodsNumber() + "的藏品上传失败,失败原因：本次上传存在重复商品");
                continue;
            }
			//判断商品的年代  20250616去除该判断条件  && failEraMap.get(goods.getGoodsEra()).compareTo(BigDecimal.ZERO)<=0
			if (MapUtils.isNotEmpty(failEraMap) && (failEraMap.containsKey(goods.getGoodsEra()))) {
				errorList.add("商品编号为：" + goods.getGoodsNumber() + "的藏品上传失败,失败原因：商品年代不存在，请下载最新模板");
				continue;
			}
            try {
                checkBatchParams(goods,Boolean.valueOf(switchFlag));
                StoreGoodsInfo storeGoodsInfo = new StoreGoodsInfo();
                try {
                    BeanUtil.copyProperties(goods,storeGoodsInfo);
                    storeGoodsInfo.setId(IDS.uniqueID());
                    storeGoodsInfo.setGoodsNo(generateGoodsNo(cityPartnerAndMerchantDTO.getCityPartner().getPartnerNo()));
                    storeGoodsInfo.setMerchantId(Long.parseLong(batchAddParam.getMerchantId()));
                    storeGoodsInfo.setCityPartnerId(cityPartnerAndMerchantDTO.getCityPartner().getId());
                    storeGoodsInfo.setSalePrice(new BigDecimal(goods.getSalePrice()));
                    storeGoodsInfo.setCurrentPrice(storeGoodsInfo.getSalePrice());
                    storeGoodsInfo.setCreateTime(LocalDateTime.now());
                    storeGoodsInfo.setStockQuantity(1);
					storeGoodsInfo.setDescription(goods.getDescription());
					//只保存第一张图片
					storeGoodsInfo.setMainImage(ImageUtils.getFirstImageUrl(JSONUtil.toJsonStr(goods.getMainImages())));
					//处理图片信息
					goodsImageInfoList.addAll(goodsImageInfoService.convertImageUrls(goods.getMainImages(), storeGoodsInfo.getId()));
                    storeGoodsInfo.setAppraisalState(StoreGoodsInfoEnum.AppraisalState.PRE_APPRAISAL.getCode());
                    storeGoodsInfo.setState(StoreGoodsInfoEnum.State.PRE_APPRAISAL.getCode());
                    storeGoodsInfo.setDelFlag(DelFlagConstants.NORMAL);

                    addList.add(storeGoodsInfo);
                    StoreGoodsImageMd5Entity entity = new StoreGoodsImageMd5Entity();
                    entity.setStoreGoodsId(storeGoodsInfo.getId());
                    entity.setMainImageMd5(goods.getMainImageMd5());
                    imageMd5s.add(entity);
                } catch (NumberFormatException e) {
                    errorList.add("商品编号为：" + goods.getGoodsNumber() + "的藏品上传失败,失败原因：" + "销售价格必须为数字");
                }
            } catch (Exception e) {
                log.error("批量上传商品异常,{}" , goods);
                errorList.add("商品编号为：" + goods.getGoodsNumber() + "的藏品上传失败,失败原因：" + e.getMessage());
                log.error(e.getMessage(),e);
            }
        }
        if(CollectionUtil.isNotEmpty(addList)) {
            saveBatch(addList);
            storeGoodsImageMd5Service.saveBatch(imageMd5s);
			//保存图片信息
			goodsImageInfoService.saveBatch(goodsImageInfoList);
        }
        return errorList;
    }

    private void checkBatchParams(AddStoreGoodsDTO goods ,Boolean switchFlag) {
        if(StringUtils.isEmpty(goods.getName())) throw  MerchantException.build(MerchantErrorEnum.GOODS_NAME_NOT_EMPTY);
        if(goods.getName().length() > 30) throw MerchantException.build(MerchantErrorEnum.GOODS_NAME_NOT_OVER_THIRTY);
        if(StringUtils.isEmpty(goods.getGoodsEra())) throw MerchantException.build(MerchantErrorEnum.GOODS_ERA_NOT_EMPTY);
        if(StringUtils.isEmpty(goods.getSalePrice())) throw MerchantException.build(MerchantErrorEnum.SALE_PRICE_NOT_EMPTY);
        if(StringUtils.isEmpty(goods.getGoodsCondition())) throw MerchantException.build(MerchantErrorEnum.GOODS_CONDITION_NOT_EMPTY);
        if(StringUtils.isEmpty(goods.getGoodsSize())) throw MerchantException.build(MerchantErrorEnum.GOODS_SIZE_NOT_EMPTY);
        if(StringUtils.isEmpty(goods.getGoodsNumber())) throw MerchantException.build(MerchantErrorEnum.GOODS_NUMBER_NOT_EMPTY);
        // 添加开关校验
        if(switchFlag && storeGoodsImageMd5Service.isExistByMainImageMd5(goods.getMainImageMd5())) {
            throw MerchantException.build(MerchantErrorEnum.GOODS_MAINIMG_MD5_EXIST);
        }
        // 敏感词检测
        try {
            TextModerationVO result = tmsService.textModeration(goods.getName(),goods.getDescription(),
					goods.getGoodsSize(),goods.getPlaceOfOrigin());
            if (!result.isPassed()) {
                throw MerchantException.build(MerchantErrorEnum.GOODS_NAME_SENSITIVE);
            }
        } catch (TencentCloudSDKException e) {
            log.error("敏感词检测服务异常", e);
            throw MerchantException.build(MerchantErrorEnum.SENSITIVE_WORD_ERROR);
        }
    }

    /**
     *  获取重复主图MD5的商品编号
     * @param storeGoods
     * @return List<String>
     */
    private List<String> getDuplicateGoodsNos(List<AddStoreGoodsDTO> storeGoods){
        // 创建一个Map集合,key为主图MD5,value为商品编号列表
        Map<String, List<String>> md5GoodsNoMap = new HashMap<>();
        for (AddStoreGoodsDTO goods : storeGoods) {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(goods.getMainImageMd5())) {
                md5GoodsNoMap.computeIfAbsent(goods.getMainImageMd5(), k -> new ArrayList<>())
                        .add(goods.getGoodsNumber());
            }
        }

        // 找出重复的mainImageMd5对应的商品编号,每个MD5只保留第一个商品编号
        List<String> duplicateGoodsNos = new ArrayList<>();
        md5GoodsNoMap.forEach((md5, goodsNos) -> {
            if (goodsNos.size() > 1) {
                // 保留第一个商品编号,其他的都添加到重复列表中
                for (int i = 1; i < goodsNos.size(); i++) {
                    duplicateGoodsNos.add(goodsNos.get(i));
                }
            }
        });
        return duplicateGoodsNos;
    }

    @Override
    public List<StoreGoodsInfoAppVO> pageForSearch(StoreGoodsSearchDTO search) {
		log.info("【StoreGoodsInfoServiceImpl.pageForSearch】 客户端藏品公示分页查询，params：{}", JSONObject.toJSONString(search));
		//构建查询条件
		QueryWrapper<StoreGoodsInfo> wrapper = new QueryWrapper<>();
		wrapper.eq("a.del_flag", '0')
				.eq("a.state", StoreGoodsInfoEnum.State.PUBLICITY.getCode())
				.and(StrUtil.isNotBlank(search.getSearchKey()), w -> w.like("a.name", search.getSearchKey())
						.or().eq("a.goods_no", search.getSearchKey())
						.or().like("m.shop_name", search.getSearchKey()))
				.in(ListTool.isNotEmpty(search.getGoodsConditions()), "a.goods_condition", search.getGoodsConditions())
				.in(ListTool.isNotEmpty(search.getGoodsEras()), "tyga.age_name", search.getGoodsEras())
				.ge(ObjUtil.isNotNull(search.getSalePriceMin()), "a.sale_price", search.getSalePriceMin())  //前端展示的：销售价格，和前端保持一致
				.le(ObjUtil.isNotNull(search.getSalePriceMax()), "a.sale_price", search.getSalePriceMax());
		//排序
		if(search.getCurrentPriceSort()!=null){
			if(search.getCurrentPriceSort()>0){
				wrapper.orderByAsc("a.sale_price");
			}else{
				wrapper.orderByDesc("a.sale_price");
			}
		}
		wrapper.orderByDesc("a.id");
		wrapper.orderByDesc("a.sort"); //数字越大，越考前
		//查询
		List<StoreGoodsInfoAppVO> list = this.baseMapper.listForSearch(PageUtil.toPage(search), wrapper);
		if (ListTool.isEmpty(list)){
			return Collections.emptyList();
		}
		log.info("【StoreGoodsInfoServiceImpl.pageForSearch】 客户端藏品公示分页查询结果size：{}", list.size());
		//处理主图
		list.parallelStream().forEach(goods -> {
			if (StrUtil.isBlank(goods.getMainImage())) {
				return;
			}
			goods.setImgUrl(imageUrlService.convertImageUrl(goods.getMainImage()).getSmallWebpUrl());
			// 店铺头像
			goods.setShopAvatar(imageUrlService.convertImageUrl(goods.getShopAvatar()).getSmallUrl());

        });
		return list;
    }

    @Override
    public GoodsStatisticVO storeGoodsCount(StoreGoodsCountDTO dto) {
        log.info("【StoreGoodsInfoServiceImpl.storeGoodsCount】 商品统计查询，params：{}", JSONObject.toJSONString(dto));
        
        // 构建返回结果
        GoodsStatisticVO result = new GoodsStatisticVO();

        // 获取藏品统计
        List<StoreGoodsCountResultVO> storeGoodsList = getStoreGoodsStatistic(dto);
        result.setStoreGoodsList(storeGoodsList);

        // 获取拍品统计
        List<GoodsLotInfoCountResultVO> lotGoodsList = getLotGoodsStatistic(dto);
        result.setLotGoodsList(lotGoodsList);

        log.info("【StoreGoodsInfoServiceImpl.storeGoodsCount】 商品统计查询结果：{}", JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 获取藏品统计数据
     * @param dto 查询条件
     * @return 藏品统计列表
     */
    private List<StoreGoodsCountResultVO> getStoreGoodsStatistic(StoreGoodsCountDTO dto) {
        // 构建查询条件
        QueryWrapper<StoreGoodsInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("a.del_flag", DelFlagConstants.NORMAL);

        // 处理区域查询条件
        if (StrUtil.isNotBlank(dto.getRegionId())) {
            // 查询该区域下的所有合伙人
            List<CityPartner> partners = baseRemoteService.getPartnerByRegionId(dto.getRegionId());
            if (CollectionUtil.isNotEmpty(partners)) {
                List<Long> partnerIds = partners.stream().map(CityPartner::getId).collect(Collectors.toList());
                wrapper.in("a.city_partner_id", partnerIds);
            } else {
                // 如果没有找到合伙人，返回空结果
                return Collections.emptyList();
            }
        }
		// 处理合伙人查询条件
		wrapper.like(StrUtil.isNotBlank(dto.getPartnerName()), "p.partner_name", dto.getPartnerName());
        // 处理商家查询条件
        wrapper.like(StrUtil.isNotBlank(dto.getShopName()), "m.shop_name", dto.getShopName());
        // 统计各状态数量（无日期范围）
        List<StoreGoodsCountResultVO> totalCountList = this.baseMapper.countByState(wrapper);
        // 处理时间范围
        wrapper.ge(dto.getStartTime() != null, "a.create_time", dto.getStartTime())
               .le(dto.getEndTime() != null, "a.create_time", dto.getEndTime());
        // 统计各状态数量（有日期范围）
        List<StoreGoodsCountResultVO> periodCountList = this.baseMapper.countByState(wrapper);
		Map<String, Long> periodCountMap = periodCountList.stream().collect(Collectors.toMap(StoreGoodsCountResultVO::getState, StoreGoodsCountResultVO::getCount));


		// 构建状态统计结果
		long sumTotalCount=0;
		long sumPeriodCount=0;
        for (StoreGoodsCountResultVO vo : totalCountList) {
            vo.setTotalCount(vo.getCount());
            vo.setStateName(StoreGoodsInfoEnum.State.getByCode(vo.getState()) != null ? StoreGoodsInfoEnum.State.getByCode(vo.getState()).getDesc() : vo.getState());
            // 该状态的统计周期的数量
            vo.setPeriodCount(periodCountMap.get(vo.getState()));
			if (ObjUtil.isNotNull(vo.getCount())) {
				sumTotalCount += vo.getCount();
			}
			if (ObjUtil.isNotNull(vo.getPeriodCount())) {
				sumPeriodCount += vo.getPeriodCount();
			}
        }
		//汇总
		totalCountList.add(new StoreGoodsCountResultVO()
				.setPeriodCount(sumPeriodCount)
				.setTotalCount(sumTotalCount)
				.setStateName("汇总"));

        return totalCountList;
    }

    /**
     * 获取拍品统计数据
     * @param dto 查询条件
     * @return 拍品统计列表
     */
    private List<GoodsLotInfoCountResultVO> getLotGoodsStatistic(StoreGoodsCountDTO dto) {
        // 构建拍品查询条件
        QueryWrapper<GoodsLotInfo> lotWrapper = buildLotGoodsQueryWrapper(dto);
        if (lotWrapper == null) {
            return Collections.emptyList();
        }

        // 统计拍品各状态数量（无日期范围）
        List<GoodsLotInfoCountResultVO> lotTotalCountList = goodsLotInfoMapper.countByState(lotWrapper);

        // 处理时间范围
        lotWrapper.ge(dto.getStartTime() != null, "a.create_time", dto.getStartTime())
                .le(dto.getEndTime() != null, "a.create_time", dto.getEndTime());

        // 统计拍品各状态数量（有日期范围）
        List<GoodsLotInfoCountResultVO> lotPeriodCountList = goodsLotInfoMapper.countByState(lotWrapper);

        // 处理拍品统计结果
        Map<String, Long> lotPeriodCountMap = lotPeriodCountList.stream()
                .collect(Collectors.toMap(GoodsLotInfoCountResultVO::getState, GoodsLotInfoCountResultVO::getCount, (v1, v2) -> v1));

        // 初始化所有状态的结果
        Map<String, GoodsLotInfoCountResultVO> allStatesMap = initAllStatesMap();

        // 填充实际查询到的数据
        fillActualData(lotTotalCountList, lotPeriodCountMap, allStatesMap);

        // 构建最终结果列表并添加汇总数据
        return buildResultListWithSummary(allStatesMap);
    }

    /**
     * 构建拍品查询条件
     * @param dto 查询条件
     * @return 查询条件包装器，如果无相关数据则返回null
     */
    private QueryWrapper<GoodsLotInfo> buildLotGoodsQueryWrapper(StoreGoodsCountDTO dto) {
        QueryWrapper<GoodsLotInfo> lotWrapper = new QueryWrapper<>();
        lotWrapper.eq("a.del_flag", DelFlagConstants.NORMAL);

        // 处理区域查询条件
        if (StrUtil.isNotBlank(dto.getRegionId())) {
            // 查询该区域下的所有合伙人
            List<CityPartner> partners = baseRemoteService.getPartnerByRegionId(dto.getRegionId());
            if (CollectionUtil.isEmpty(partners)) {
                // 如果没有找到合伙人，返回null
                return null;
            }
            List<Long> partnerIds = partners.stream().map(CityPartner::getId).collect(Collectors.toList());
            lotWrapper.in("a.city_partner_id", partnerIds);
        }

        // 处理合伙人查询条件
        lotWrapper.like(StrUtil.isNotBlank(dto.getPartnerName()), "p.partner_name", dto.getPartnerName());
        // 处理商家查询条件
        lotWrapper.like(StrUtil.isNotBlank(dto.getShopName()), "m.shop_name", dto.getShopName());

        return lotWrapper;
    }

    /**
     * 初始化所有状态的结果映射
     * @return 所有状态的结果映射
     */
    private Map<String, GoodsLotInfoCountResultVO> initAllStatesMap() {
        Map<String, GoodsLotInfoCountResultVO> allStatesMap = new HashMap<>();
        for (GoodsLotInfoEnum.State state : GoodsLotInfoEnum.State.values()) {
            GoodsLotInfoCountResultVO vo = new GoodsLotInfoCountResultVO();
            vo.setState(state.getCode());
            vo.setStateName(state.getDesc());
            vo.setCount(0L);
            vo.setTotalCount(0L);
            vo.setPeriodCount(0L);
            allStatesMap.put(state.getCode(), vo);
        }
        return allStatesMap;
    }

    /**
     * 填充实际查询到的数据
     * @param lotTotalCountList 总计数据列表
     * @param lotPeriodCountMap 周期数据映射
     * @param allStatesMap 所有状态的结果映射
     */
    private void fillActualData(List<GoodsLotInfoCountResultVO> lotTotalCountList,
                                Map<String, Long> lotPeriodCountMap,
                                Map<String, GoodsLotInfoCountResultVO> allStatesMap) {
        for (GoodsLotInfoCountResultVO vo : lotTotalCountList) {
            GoodsLotInfoCountResultVO stateVo = allStatesMap.get(vo.getState());
            if (stateVo != null) {
                stateVo.setCount(vo.getCount());
                stateVo.setTotalCount(vo.getCount());
                stateVo.setPeriodCount(lotPeriodCountMap.getOrDefault(vo.getState(), 0L));
            }
        }
    }

    /**
     * 构建最终结果列表并添加汇总数据
     * @param allStatesMap 所有状态的结果映射
     * @return 最终结果列表
     */
    private List<GoodsLotInfoCountResultVO> buildResultListWithSummary(Map<String, GoodsLotInfoCountResultVO> allStatesMap) {
        List<GoodsLotInfoCountResultVO> resultList = new ArrayList<>(allStatesMap.values());

        long lotSumTotalCount = 0;
        long lotSumPeriodCount = 0;

        for (GoodsLotInfoCountResultVO vo : resultList) {
            if (ObjUtil.isNotNull(vo.getTotalCount())) {
                lotSumTotalCount += vo.getTotalCount();
            }
            if (ObjUtil.isNotNull(vo.getPeriodCount())) {
                lotSumPeriodCount += vo.getPeriodCount();
            }
        }

        // 添加汇总行
        resultList.add(new GoodsLotInfoCountResultVO()
                .setPeriodCount(lotSumPeriodCount)
                .setTotalCount(lotSumTotalCount)
                .setStateName("汇总"));

        return resultList;
    }

	@Override
	public StoreGoodsInfoAppPageVO pageForSearchV2(GoodsSearchV2DTO search) {
		log.info("【StoreGoodsInfoServiceImpl.pageForSearchV2】 公示拍品分页查询，params：{}", JSONObject.toJSONString(search));

		if (StringUtils.isBlank(search.getSearchKey())) {
			return new StoreGoodsInfoAppPageVO(Collections.emptyList());
		}

		Long timestamp = search.getTimestamp();
		if (ObjUtil.isNull(timestamp)) {
			timestamp = System.currentTimeMillis() / 1000;
		}

		SearchRequest searchRequest = new SearchRequest(EsIndexEnum.STORE_GOODS.getIndexName());
		searchRequest.preference(String.valueOf(timestamp));

		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
		boolQuery.must(
				QueryBuilders.boolQuery()
						.should(QueryBuilders.matchQuery("name", search.getSearchKey()))
						.should(
								QueryBuilders.matchQuery("name", search.getSearchKey())
										.operator(Operator.AND)
										.analyzer("ik_smart")
										.boost(1.5F)
						)
						.should(QueryBuilders.matchQuery("shopName", search.getSearchKey()).boost(0.8F))
						.should(QueryBuilders.termQuery("shopName.raw", search.getSearchKey()))
						.should(QueryBuilders.termQuery("goodsNo", search.getSearchKey()))
		);

		if (ListTool.isNotEmpty(search.getGoodsConditions())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsCondition", search.getGoodsConditions()));
		}

		if (ListTool.isNotEmpty(search.getGoodsEras())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsEra", search.getGoodsEras()));
		}

		RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("salePrice");
		if (ObjUtil.isNotNull(search.getSalePriceMin())) {
			rangeQueryBuilder.gte(search.getSalePriceMin());
		}
		if (ObjUtil.isNotNull(search.getSalePriceMax())) {
			rangeQueryBuilder.lte(search.getSalePriceMax());
		}
		if (ObjUtil.isNotNull(search.getSalePriceMin()) || ObjUtil.isNotNull(search.getSalePriceMax())) {
			boolQuery.filter(rangeQueryBuilder);
		}

		// 防止重复数据，比如翻页时新上架了藏品
//		boolQuery.filter(QueryBuilders.rangeQuery("publicityStartTime").lte(timestamp));

		// 只查询公示中的
		boolQuery.filter(QueryBuilders.termQuery("state", StoreGoodsInfoEnum.State.PUBLICITY.getCode()));

		if (ObjUtil.isNotNull(search.getCurrentPriceSort())) {
			if (search.getCurrentPriceSort() > 0) {
				sourceBuilder.sort("salePrice", SortOrder.ASC);
			} else {
				sourceBuilder.sort("salePrice", SortOrder.DESC);
			}
		}
		sourceBuilder.sort("_score", SortOrder.DESC);
		sourceBuilder.sort("id", SortOrder.DESC);

		// 上次排序的值
		if (ArrayUtil.isNotEmpty(search.getSortValues())) {
			sourceBuilder.searchAfter(search.getSortValues());
		}

		sourceBuilder.size(search.getSize().intValue());
		sourceBuilder.query(boolQuery);
		searchRequest.source(sourceBuilder);
		SearchResponse searchResponse = esService.search(searchRequest);
		if (searchResponse == null || searchResponse.status().getStatus() != 200) {
			return new StoreGoodsInfoAppPageVO(Collections.emptyList());
		}

		StoreGoodsInfoAppPageVO page = getStoreGoodsPage(searchResponse);
		page.setTimestamp(timestamp);
		log.info("【pageForSearchV2】 公示拍品分页查询结果size：{}", page.getRecords().size());
		return page;
	}

	private StoreGoodsInfoAppPageVO getStoreGoodsPage(SearchResponse searchResponse) {

		Object[] sortValues = new Object[0];
		List<StoreGoodsInfoAppVO> records = new ArrayList<>();
		for (SearchHit hit : searchResponse.getHits().getHits()) {

			Long id = Long.valueOf(hit.getId());
			Map<String, Object> sourceAsMap = hit.getSourceAsMap();
			String mainImage = MapUtils.getString(sourceAsMap, "mainImage");
			String shopAvatar = MapUtils.getString(sourceAsMap, "shopAvatar");

			StoreGoodsInfoAppVO goods = new StoreGoodsInfoAppVO();
			goods.setId(id);
			goods.setName(MapUtils.getString(sourceAsMap, "name"));
			goods.setShopName(MapUtils.getString(sourceAsMap, "shopName"));
			goods.setState(MapUtils.getString(sourceAsMap, "state"));
			goods.setMainImage(mainImage);
			goods.setImgUrl(imageUrlService.convertImageUrl(mainImage).getSmallWebpUrl());
			goods.setShopAvatar(imageUrlService.convertImageUrl(shopAvatar).getSmallUrl());
			goods.setSalePrice(new BigDecimal(MapUtils.getString(sourceAsMap, "salePrice")));
			goods.setGoodsEra(MapUtils.getString(sourceAsMap, "goodsEra"));
			goods.setMerchantId(MapUtils.getLong(sourceAsMap, "merchantId"));
			goods.setBenefitLevel(MapUtils.getString(sourceAsMap, "benefitLevel"));
			goods.setTranName(MapUtils.getString(sourceAsMap, "tranName"));
			goods.setTranCode(MapUtils.getString(sourceAsMap, "tranCode"));
			goods.setTranIcon(MapUtils.getString(sourceAsMap, "tranIcon"));
			records.add(goods);

			sortValues = hit.getSortValues();
		}

		StoreGoodsInfoAppPageVO page = new StoreGoodsInfoAppPageVO();
		page.setRecords(records);
		page.setSortValues(sortValues);
		return page;
	}

	@Override
	public StoreGoodsInfoAppPageVO pageForRecommendV1(GoodsRecommendV1DTO dto) {
		log.info("【StoreGoodsInfoServiceImpl.pageForRecommendV1】 公示拍品分页查询，params：{}", JSONObject.toJSONString(dto));

		SearchRequest searchRequest = new SearchRequest(EsIndexEnum.STORE_GOODS.getIndexName());
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

		Long seed = dto.getSeed();
		Object[] sortValues = dto.getSortValues();
		if (ObjUtil.isNull(seed) || ArrayUtil.isEmpty(sortValues)) {
			seed = Math.abs(RandomUtil.randomLong());
			sortValues = new Object[0];
		}

		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
		if (ListTool.isNotEmpty(dto.getGoodsConditions())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsCondition", dto.getGoodsConditions()));
		}

		if (ListTool.isNotEmpty(dto.getGoodsEras())) {
			boolQuery.filter(QueryBuilders.termsQuery("goodsEra", dto.getGoodsEras()));
		}

		RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("salePrice");
		if (ObjUtil.isNotNull(dto.getSalePriceMin())) {
			rangeQueryBuilder.gte(dto.getSalePriceMin());
		}
		if (ObjUtil.isNotNull(dto.getSalePriceMax())) {
			rangeQueryBuilder.lte(dto.getSalePriceMax());
		}
		if (ObjUtil.isNotNull(dto.getSalePriceMin()) || ObjUtil.isNotNull(dto.getSalePriceMax())) {
			boolQuery.filter(rangeQueryBuilder);
		}

		// 只查询公示中的
		boolQuery.filter(QueryBuilders.termQuery("state", StoreGoodsInfoEnum.State.PUBLICITY.getCode()));

		FunctionScoreQueryBuilder functionScoreQuery = QueryBuilders.functionScoreQuery(
				boolQuery,
				new RandomScoreFunctionBuilder().seed(seed).setField("id")
		);
		functionScoreQuery.boostMode(CombineFunction.REPLACE);

		// 上次排序的值
		if (ArrayUtil.isNotEmpty(sortValues)) {
			sourceBuilder.searchAfter(sortValues);
		}
//		sourceBuilder.sort("sort", SortOrder.DESC);
		sourceBuilder.sort("_score", SortOrder.DESC);
		sourceBuilder.sort("id", SortOrder.DESC);

		sourceBuilder.size(dto.getSize().intValue());
		sourceBuilder.query(functionScoreQuery);
		searchRequest.source(sourceBuilder);
		SearchResponse searchResponse = esService.search(searchRequest);
		if (searchResponse == null || searchResponse.status().getStatus() != 200) {
			return new StoreGoodsInfoAppPageVO(Collections.emptyList());
		}

		StoreGoodsInfoAppPageVO page = getStoreGoodsPage(searchResponse);
		page.setSeed(seed);
		log.info("【pageForRecommend】 公示拍品分页查询结果size：{}", page.getRecords().size());
		return page;
	}

	@Override
	public boolean syncStoreGoods2ES(List<Long> ids) {

		List<StoreGoodsModel> modelList = this.baseMapper.queryEsModelByIds(ids);
		Map<String, StoreGoodsModel> modelMap = modelList.stream().collect(Collectors.toMap(StoreGoodsModel::getId, Function.identity()));

		BulkRequest bulkRequest = new BulkRequest();
		ids.stream().map(String::valueOf).forEach(id -> {
			StoreGoodsModel model = modelMap.get(id);
			if (model != null) {
				IndexRequest indexRequest = new IndexRequest();
				indexRequest.index(model.getIndex().getIndexName());
				indexRequest.id(model.getId());
				indexRequest.source(model.toMap());
				bulkRequest.add(indexRequest);
			} else {
				DeleteRequest deleteRequest = new DeleteRequest();
				deleteRequest.index(EsIndexEnum.STORE_GOODS.getIndexName());
				deleteRequest.id(id);
				bulkRequest.add(deleteRequest);
			}
		});

		// 同步es
		return esService.bulk(bulkRequest);
	}
}
