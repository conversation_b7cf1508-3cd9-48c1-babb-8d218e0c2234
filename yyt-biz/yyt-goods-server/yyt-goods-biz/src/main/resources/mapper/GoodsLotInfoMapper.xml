<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.goods.mapper.GoodsLotInfoMapper">

    <resultMap type="com.yts.yyt.goods.api.entity.GoodsLotInfo" id="GoodsLotInfoMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="goodsId" column="goods_id" jdbcType="INTEGER"/>
        <result property="cityPartnerId" column="city_partner_id" jdbcType="INTEGER"/>
        <result property="merchantId" column="merchant_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="placeOfOrigin" column="place_of_origin" jdbcType="VARCHAR"/>
        <result property="salePrice" column="sale_price" jdbcType="NUMERIC"/>
        <result property="goodsNo" column="goods_no" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="goodsCondition" column="goods_condition" jdbcType="VARCHAR"/>
        <result property="mainImage" column="main_image" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>


	<select id="listForSearch" resultType="com.yts.yyt.goods.api.vo.GoodsLotInfoAppVO">
		select a.id,
		       a.goods_id,
		       a.sale_price,
		       a.name,
		       a.main_image,
		       a.state,
			   tyga.age_name as goodsEra,
		       a.merchant_id,
		       m.shop_name,
		       m.benefit_level,
			   m.shop_avatar shopAvatar,
		       ts.tran_name,
		       ts.tran_code,
		       ts.tran_icon
		from goods_lot_info a
		left join merchant_info m on a.merchant_id = m.id
		left join transaction_setting ts on m.benefit_level = ts.tran_code
		left join t_yj_goods_age tyga on a.era_id = tyga.id
		${ew.customSqlSegment}
	</select>

	<select id="queryPageData" resultType="com.yts.yyt.goods.api.vo.GoodsLotInfoAppVO">
		select
			a.id,
			a.sale_price,
			a.name,
			a.main_image,
			a.state,
			tyga.age_name as goodsEra,
			a.goods_no,
			a.merchant_id,
			uha.status as activeStatus
		from goods_lot_info a
		left join t_yj_goods_age tyga on a.era_id = tyga.id
		left join user_huifu_account uha on a.merchant_id = uha.merchant_id
		${ew.customSqlSegment}
	</select>

	<select id="queryDetail" resultType="com.yts.yyt.goods.api.vo.GoodsLotInfoVO">
		select a.id,
		       a.goods_id,
			   a.goods_no,
			   a.sale_price,
			   a.name,
			   a.main_image,
			   a.state,
			   tyga.age_name as goodsEra,
			   a.goods_condition,
			   a.merchant_id,
			   a.city_partner_id,
			   a.source_lot_id,
			   a.create_time,
			   a.order_no as orderNo,
		       sgi.place_of_origin,
		       sgi.description,
		       sgi.goods_size,
		       sgi.main_image,
			   tyga.age_name as appraisalEra,
		       sgi.appraisal_time,
		       sgi.appraise_price,
		       sgi.appraisal_state,
		       sgi.appraisal_level,
		       sgi.appraisal_authenticity,
		       sgi.publicity_time,
		       sgi.publicity_start_time,
			   m.shop_name,
			   m.contact_person_name,
			   m.contact_phone as merchantContactPhone,
			   m.shop_type,
			   m.benefit_level,
			   m.shop_avatar shopAvatar,
			   m.user_id merchantUserId,
			   ts.tran_name,
			   ts.tran_code,
			   ts.tran_icon
		    from goods_lot_info a
		    left join store_goods_info sgi on a.goods_id = sgi.id
			left join merchant_info m on a.merchant_id = m.id
			left join transaction_setting ts on m.benefit_level = ts.tran_code
			left join t_yj_goods_age tyga on a.era_id = tyga.id
			where a.id = #{id}
	</select>

	<select id="getBasePage" resultType="com.yts.yyt.goods.api.vo.GoodsLotInfoAdminVO">
		SELECT
			gi.id,
			gi.goods_no,
			gi.sale_price,
			gi.name,
			gi.main_image,
			gi.state,
			tyga.age_name as goodsEra,
			gi.goods_condition,
			gi.merchant_id,
			gi.city_partner_id,
			gi.create_time,
			gi.update_time,
			sgi.place_of_origin,
			sgi.goods_size,
			sgi.appraisal_state,
			m.contact_person_name,
			m.shop_avatar shopAvatar,
			m.contact_phone as merchantContactPhone,
			m.shop_type,
			m.shop_name,
			uha.status as activeStatus,
			gi.dist_enable,
			gi.wait_audit
		from goods_lot_info gi
		left join store_goods_info sgi on gi.goods_id = sgi.id
		left join merchant_info m on gi.merchant_id = m.id
		left join user_huifu_account uha on gi.merchant_id = uha.merchant_id
		left join t_yj_goods_age tyga on gi.era_id = tyga.id
		where gi.del_flag = 0
		<if test="p.name != null and p.name !=''">
			and  gi.name like concat('%',#{p.name},'%')
		</if>
		<if test="p.shopName != null and p.shopName !=''">
			and  m.shop_name like concat('%',#{p.shopName},'%')
		</if>
		<if test="p.state != null and p.state !=''">
			and  gi.state  = #{p.state}
		</if>
		<if test="p.goodsNo != null and p.goodsNo !=''">
			and  gi.goods_no  = #{p.goodsNo}
		</if>
		<if test="p.merchantContactPhone != null and p.merchantContactPhone !=''">
			and  m.contact_phone = #{p.merchantContactPhone}
		</if>
		<if test="p.merchantId != null and p.merchantId !=''">
			and  (gi.city_partner_id  = #{p.merchantId} or gi.merchant_id = #{p.merchantId})
		</if>
		<if test="p.merchantIds != null and p.merchantIds.size() > 0">
			and m.id in
			<foreach item="merchantId" index="index" collection="p.merchantIds" open="(" separator="," close=")">
				#{merchantId}
			</foreach>
		</if>
		<if test="p.startCreateTime !=null and p.startCreateTime !='' and p.endCreateTime !=null and p.endCreateTime !=''">
			and gi.create_time BETWEEN #{p.startCreateTime} and #{p.endCreateTime}
		</if>
		order by gi.id desc
	</select>

	<select id="getCounts" resultType="com.yts.yyt.goods.api.vo.GoodsLotCountVO">
		SELECT
		count( 1 ) AS allCount,
		SUM( CASE WHEN `state` = 'wait_selling' THEN 1 ELSE 0 END ) As waitSellingCount ,
		SUM( CASE WHEN `state` = 'selling' THEN 1 ELSE 0 END ) As sellingCount ,
		SUM( CASE WHEN `state` = 'locking' THEN 1 ELSE 0 END ) As lockingCount ,
		SUM( CASE WHEN `state` = 'sold' THEN 1 ELSE 0 END ) As soldCount
		FROM
		goods_lot_info gi
		WHERE
		gi.del_flag = 0
		<if test="p.merchantId != null and p.merchantId !=''">
			and  (gi.city_partner_id  = #{p.merchantId} or gi.merchant_id = #{p.merchantId})
		</if>
	</select>
	<select id="shopGoodsCounts" resultType="com.yts.yyt.goods.api.vo.ShopGoodsLotCountVO">
		SELECT
		COUNT(DISTINCT CASE WHEN `state` = 'wait_selling' THEN gi.id ELSE NULL END) AS waitSellingCount,
		COUNT(DISTINCT CASE WHEN `state` = 'selling' THEN gi.id ELSE NULL END) AS sellingCount,
		COUNT(DISTINCT CASE WHEN `state` = 'sold' THEN gi.id ELSE NULL END) AS soldCount
		FROM
		goods_lot_info gi
		left join t_yj_goods_age tyga on gi.era_id = tyga.id
		WHERE
		gi.del_flag = 0
		and tyga.status = 1
		<if test="merchantId != null">
			and gi.merchant_id = #{merchantId}
		</if>
	</select>
	
	<select id="countByState" resultType="com.yts.yyt.goods.api.vo.GoodsLotInfoCountResultVO">
		SELECT
		    a.state,
		    COUNT(1) AS count
		FROM
		    goods_lot_info a
			left join city_partner p on a.city_partner_id = p.id
			left join merchant_info m on a.merchant_id = m.id
		    ${ew.customSqlSegment}
		GROUP BY
		    a.state
	</select>

	<select id="queryEsModelByIds" parameterType="java.util.List" resultType="com.yts.yyt.common.es.model.GoodsLotModel">
		select a.id,
			   a.goods_id,
			   a.sale_price,
			   a.name,
			   a.main_image,
			   a.state,
			   a.dist_enable,
			   tyga.age_name as goodsEra,
			   a.merchant_id,
			   a.goods_no,
			   a.goods_condition,
			   m.shop_name,
			   m.benefit_level,
			   m.shop_avatar shopAvatar,
			   ts.tran_name,
			   ts.tran_code,
			   ts.tran_icon,
			   a.`sort`,
			   a.create_time,
			   a.update_time
		from goods_lot_info a
		left join merchant_info m on a.merchant_id = m.id
		left join transaction_setting ts on m.benefit_level = ts.tran_code
		left join t_yj_goods_age tyga on a.era_id = tyga.id
		where a.id in
		<foreach collection="list" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
		  and a.`state` = 'selling'
		  and a.`del_flag` = '0' and tyga.`status` = 1 and m.huifu_id is not null
		group by a.id
	</select>

</mapper>

