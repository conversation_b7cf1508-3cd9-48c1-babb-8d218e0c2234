package com.yts.yyt.goods.api.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsBizErrorEnum {

    SELF_SUPPORT_GOODS_NOT_SUBMIT_APPRAISAL(10025, "自营藏品无需提交鉴定"),
	NOT_EXIST_APPRAISE_AGE(10024, "鉴定年代不存在"),
	NOT_ALLOW_DELETE_FAIL_GOODS(10023, "年代不合规的失败藏品不允许删除"),
	CANNOT_DELETE_FAIL_GOODS_ROLE(10022, "该角色无权删除失败藏品"),
	NOT_ALLOW_DELETE(10021, "待鉴定/鉴定失败状态才可删除"),
	//店铺还未激活
	SHOP_NOT_ACTIVATED_ERROR(10020, "店铺尚未激活"),
	QUERY_USER_HUIFU_INFO_ERROR(10019, "查询用户汇付账户信息异常"),
	RESELL_ERROR(10018, "转售异常"),
	QUERY_MERCHANT_INFO_ERROR(10017, "查询商户信息异常"),
	GOODS_NAME_SENSITIVE_ERROR(10016, "商品名称包含敏感词：{}"),
	GOODS_DETAIL_SENSITIVE_ERROR(10015, "商品描述包含敏感词：{}"),
	GOODS_SENSITIVE_CHECK_ERROR(10014, "敏感词检测服务异常"),
	GOODS_CANNOT_DEL_ERROR(10013, "已销售状态数据才可删除"),
    GOODS_ZIP_UPLOAD_BUSY_ERROR(10012, "压缩包文件尚在处理中，请稍后重试"),
    GOODS_NO_REPEAT_ERROR(10011, "商品编码已存在"),
    GOODS_NO_NULL_ERROR(10010, "商品编码不能为空"),
    GOODS_OUT_SYSTEM_ERROR(10009, "调用外部系统繁忙，请稍后重试"),
    GOODS_CANNOT_EDIT_TO_PUBLICITY_ERROR(10008, "只有待公示/仓库中状态可变更"),
    GOODS_CANNOT_EDIT_ERROR(10007, "锁定/已销售状态不可编辑"),
    GOODS_IS_NULL_ERROR(10006, "未查询到商品信息"),
    GOODS_ADD_STATE_ERROR(10005, "新增商品状态只能是待公示或公示中"),
    PARAMS_ERROR(10004, "参入异常"),
    NOT_REMOVE_CATEGORY(10003, "商品分类已关联商品，不可删除"),
    FILE_EMPTY(10002, "上传文件不能为空"),
    NOT_LOGIN(10001, "请先登录"),
    AUDIT_FAILED(10007, "审核失败"),
    DATA_NOT_EXIST(10008, "数据不存在"),
    GOODS_NOT_EXIST(10009, "商品不存在"),
    INCENTIVE_CONF_NOT_EXIST(10010, "激励配置不存在"),

	;
    private final Integer code;
    private final String msg;

    public void throwException() {
    	throw new GoodsException(this.msg,this.code);
    }

}
