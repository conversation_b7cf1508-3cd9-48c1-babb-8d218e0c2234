package com.yts.yyt.goods.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "新增商品类型")
public class AddGoodsCategoryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "父类id")
    private Long goodsTypeId;

    @Schema(description = "父类名称")
    private String goodsTypeName;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "是否显示（1显示  0不显示）")
    private Integer isShow;

    @Schema(description = "是否推荐(1 推荐 0 不推荐)")
    private Integer isRecommend;

    @Schema(description = "排序")
    private Integer sort;
}
