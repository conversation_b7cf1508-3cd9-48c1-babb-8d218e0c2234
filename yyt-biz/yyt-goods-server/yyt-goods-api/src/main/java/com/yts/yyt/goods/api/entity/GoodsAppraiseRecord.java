package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("goods_appraise_record")
@Schema(description = "商品鉴定明细表")
@Accessors(chain = true)
public class GoodsAppraiseRecord extends Model<GoodsAppraiseRecord> {

    @Schema(description="主键")
    private Long id;

    @Schema(description="商品id")
    private Long goodsId;

    @Schema(description="鉴定估价（元）")
    private BigDecimal appraisePrice;

    @Schema(description="鉴定时间")
    private LocalDateTime appraiseTime;

    @Schema(description="鉴定断代")
    private String appraisalEra;

    @Schema(description="是否官窑 0-否; 1-是")
    private String appraisalGuanWare;

    @Schema(description="专家编号")
    private String appraisalExpertNum;

    @Schema(description="创建时间")
    private LocalDateTime createTime;

    @Schema(description="更新时间")
    private LocalDateTime updateTime;

    @Schema(description="删除标志 0-正常、1-删除")
    private String delFlag;
}
