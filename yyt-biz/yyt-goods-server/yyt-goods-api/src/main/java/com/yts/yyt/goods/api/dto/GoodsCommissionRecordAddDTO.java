package com.yts.yyt.goods.api.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yts.yyt.common.core.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "商品佣金明细表")
public class GoodsCommissionRecordAddDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 鉴定估价
     */
    @Schema(description = "鉴定估价")
    @NotNull(message = "鉴定估价不能为空")
    private BigDecimal appraisePrice;

    /**
     * 合伙人id
     */
    @Schema(description = "合伙人id")
    @NotNull(message = "合伙人id不能为空")
    private Long cityPartnerId;

    /**
     * 藏品ID
     */
    @Schema(description = "藏品ID")
    @NotNull(message = "藏品ID不能为空")
    private Long goodsId;

    /**
     * 商户id
     */
    @Schema(description = "商户id")
    @NotNull(message = "商户id不能为空")
    private Long merchantId;

    /**
     * 鉴定时间
     */
    @Schema(description = "鉴定时间")
    @NotNull(message = "鉴定时间不能为空")
    private LocalDateTime appraisalTime;

}

