package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("goods_chain_batch")
@Schema(description = "批次")
public class GoodsChainBatchEntity extends Model<GoodsChainBatchEntity> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "平台商品ID")
    private String goodsNo;

    @Schema(description = "平台商品名称")
    private String goodsName;

    @Schema(description = "蚂蚁链商品id")
    private String chainProductId;

    @Schema(description = "商户id")
    private String merchantId;

    @Schema(description = "上链内容")
    private String chainContent;

    @Schema(description = "批次Id")
    private String batchId;

    @Schema(description = "溯源码")
    private String traceCode;

    @Schema(description = "状态")
    private Byte status;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}


