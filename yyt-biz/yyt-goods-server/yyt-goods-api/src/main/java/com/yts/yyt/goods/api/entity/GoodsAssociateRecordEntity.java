package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 商品联想词搜索记录
 * @TableName goods_associate_record
 */
@TableName(value ="goods_associate_record")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品批次号管理")
@Data
public class GoodsAssociateRecordEntity extends Model<GoodsBatchNoEntity> {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 联想词id
     */
    private Long associateId;

    /**
     * 联想词类型
     */
    private String associateType;

    /**
     * 联想词名称
     */
    private String associateName;

    /**
     * 搜索次数
     */
    private Long searchTimes;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}