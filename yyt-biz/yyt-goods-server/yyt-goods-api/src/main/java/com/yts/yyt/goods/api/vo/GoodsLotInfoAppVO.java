package com.yts.yyt.goods.api.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Schema(description = "商品服务返回")
public class GoodsLotInfoAppVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @Schema(description = "藏品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long goods_id;

    @Schema(description = "商品名称")
    private String name;

    @Schema(description = "商品主图")
    private String mainImage;

    /**
     * 主图第一张： _ms
     */
    @Schema(description = "商品主图-第一张  ")
    private String imgUrl;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

	/**@see GoodsInfoEnum.State */
	@Schema(description = "状态: selling 销售中, sold 已销售, in_warehouse 仓库中")
	private String state;

	@Schema(description = "商品年代")
    private String goodsEra;

	@Schema(description = "店铺id")
    private Long merchantId;

	@Schema(description = "店铺名称")
	private String shopName;

	/**
	 * 店铺头像
	 */
	@Schema(description = "店铺头像")
	private String shopAvatar;

	@Schema(description = "权益配置等级")
	private String benefitLevel;

	@Schema(description = "权益等级名称")
	private String tranName;

	@Schema(description = "权益等级编码")
	private String tranCode;

	@Schema(description = "权益等级logo")
	private String tranIcon;

	@Schema(description = "激活状态： 3-激活成功，其他-未激活")
	private Integer activeStatus ;
}
