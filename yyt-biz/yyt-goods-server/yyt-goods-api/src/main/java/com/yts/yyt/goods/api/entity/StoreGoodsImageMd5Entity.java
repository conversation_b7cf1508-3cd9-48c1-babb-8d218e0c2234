package com.yts.yyt.goods.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 商品主图MD5信息
 */
@Data
@TableName("store_goods_image_md5")
@EqualsAndHashCode(callSuper = true)
public class StoreGoodsImageMd5Entity extends Model<StoreGoodsImageMd5Entity> {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private Long id;

    /**
     * 商品ID
     */
    private Long storeGoodsId;
    
    /**
     * 主图MD5
     */
    private String mainImageMd5;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 删除标志 0-正常、1-删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标志 0-正常、1-删除")
    private String delFlag;

}