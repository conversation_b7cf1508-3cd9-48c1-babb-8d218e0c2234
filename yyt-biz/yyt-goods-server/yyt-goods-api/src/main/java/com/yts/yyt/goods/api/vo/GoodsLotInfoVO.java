package com.yts.yyt.goods.api.vo;

import com.yts.yyt.goods.api.dto.ImageUrlDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "拍品信息-vo")
public class GoodsLotInfoVO   {

	/**
	 * 拍品id
	 */
	@Schema(description="拍品id")
	private Long id;
	/**
	 * 藏品id
	 */
	@Schema(description="藏品id")
	private Long goodsId;
	/**
	 * 合伙人id
	 */
	@Schema(description="合伙人id")
	private Long cityPartnerId;
	/**
	 * 归属id
	 */
	@Schema(description="归属id")
	private Long merchantId;
	/**
	 * 名称
	 */
	@Schema(description="名称")
	private String name;
	/**
	 *  窑口
	 */
	@Schema(description=" 窑口")
	private String placeOfOrigin;
	/**
	 * 销售价格
	 */
	@Schema(description="销售价格")
	private BigDecimal salePrice;
	/**
	 * 商品编码
	 */
	@Schema(description="商品编码")
	private String goodsNo;
	/**
	 * wait_selling 待上架、 selling 销售中、 locking 锁定中、 sold 已销售
	 */
	@Schema(description="wait_selling 待上架、 selling 销售中、 locking 锁定中、 sold 已销售")
	private String state;
	/**
	 * 商品年代
	 */
	@Schema(description="商品年代")
	private String goodsEra;
	/**
	 * 商品品相
	 */
	@Schema(description="商品品相")
	private String goodsCondition;
	/**
	 * 商品主图-原图
	 */
	@Schema(description="商品主图-原图")
	private String mainImage;

	/**
	 * 商品主图list
	 */
	@Schema(description="商品主图list")
	private List mainImageList;

	/**
	 * 原图list
	 */
	@Schema(description="原图list")
	private List<ImageUrlDTO> originImageUrl;

    /**
     * 主图第一张： _ms
     *  
     */
    @Schema(description = "商品主图-第一张-切割图")
    private String imgUrl;
    
	/**
	 * 排序
	 */
	@Schema(description="排序")
	private Integer sort;

	/**
	 * 尺寸
	 */
	@Schema(description="尺寸")
	private String goodsSize;

	/**
	 * 描述
	 */
	@Schema(description="描述")
	private String description;



	/**
	 * 订单编号
	 */
	@Schema(description="订单编号")
	private String orderNo;

	/**
	 * 原拍品id： 转售场景下有值
	 */
	@Schema(description="原拍品id")
	private Long sourceLotId;

	/**
	 * 店铺类型
	 */
	@Schema(description = "店铺类型")
	private Integer shopType;
	/**
	 * 店铺类型
	 */
	@Schema(description = "店铺类型名称")
	private String shopTypeName;
	/**
	 * 店铺头像
	 */
	@Schema(description = "店铺头像")
	private String shopAvatar;

	@Schema(description = "店铺名称")
	private String shopName;

	@Schema(description = "权益配置等级")
	private String benefitLevel;

	@Schema(description = "权益等级名称")
	private String tranName;

	@Schema(description = "权益等级编码")
	private String tranCode;

	@Schema(description = "权益等级logo")
	private String tranIcon;

	/**
	 * 店铺联系人姓名
	 */
	@Schema(description = "店铺联系人姓名")
	private String contactPersonName;

	/**
	 * 店铺联系电话
	 */
	@Schema(description = "店铺联系电话")
	private String merchantContactPhone;

	/**
	 * 上链信息
	 */
	@Schema(description="上链信息")
	private String upperChainInfo;

	/**
	 * 鉴定年代
	 */
	@Schema(description="鉴定年代")
	private String appraisalEra;
	/**
	 * 鉴定时间
	 */
	@Schema(description="鉴定时间")
	private LocalDateTime appraisalTime;

	/**
	 * 鉴定估值
	 */
	@Schema(description="鉴定估值")
	private String appraisePrice;
	/**
	 * 鉴定状态
	 */
	@Schema(description="鉴定状态 1-鉴定成功")
	private String appraisalState;
	/**
	 * 鉴定状态
	 */
	@Schema(description="鉴定级别")
	private String appraisalLevel;
	/**
	 * 鉴定结果
	 */
	@Schema(description="鉴定结果 0-真，1-假")
	private String appraisalAuthenticity;

	@Schema(description = "公示时间(天)")
	private Integer publicityTime;

	@Schema(description = "公示开始时间")
	private LocalDateTime publicityStartTime;

	@Schema(description = "创建时间")
	private LocalDateTime createTime;

    /**
     * 店铺商家移动端用户id
     */
    @Schema(description = "店铺商家移动端用户id")
    private Long  merchantUserId;

	/**
	 * 佣金金额
	 */
	@Schema(description = "佣金金额")
	private BigDecimal commissionAmount;

	/**
	 * 是否是分销员
	 */
	@Schema(description = "是否是分销员")
	private Integer isDistributor;

}

