package com.yts.yyt.goods.api.dto;

import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 商品查询功能走rom
 * <AUTHOR>
 * @date 2025/1/11
 */
@Data
@Schema(description = "查询商品")
public class SearchGoodsDTO {

    @Schema(description = "查询的关键字")
    private String searchKey;

    @Schema(description = "展示类型")
    private String showTag;

    @Schema(description = "展示类型-多个")
    private List<String> showTags;

    @Schema(description = "类型")
    private List<Long> goodsCategoryIds;

    @Schema(description = "年代")
    private List<Long> goodsEraIds;

    @Schema(description = "颜色")
    private List<Long> goodsColorIds;

    @Schema(description = "纹路")
    private List<Long> goodsPatternIds;

    @Schema(description = "器型")
    private List<Long> goodsShapeIds;

    @Schema(description = "当前价格最小")
    private BigDecimal currentPriceMin;

    @Schema(description = "当前价格最大")
    private BigDecimal currentPriceMax;

    @Schema(description = "价格排序(1 正序 -1 倒序)")
    private Integer currentPriceSort;

    /**@see GoodsInfoEnum.Type */
    @Schema(description = "类型",hidden = true)
    private String type;

    /**@see GoodsInfoEnum.State */
    @Schema(description = "类型",hidden = true)
    private String state;

	@Schema(description = "id")
	private Long associateId;

	/**
	 * @see com.yts.yyt.goods.api.enums.GoodsInfoEnum.AssociateType
	 */
	@Schema(description = "联想类型")
	private String associateType;

}
