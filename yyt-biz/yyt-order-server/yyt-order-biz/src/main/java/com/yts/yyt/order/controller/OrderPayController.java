package com.yts.yyt.order.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.order.api.dto.*;
import com.yts.yyt.order.api.dto.admin.AdminOrderLogisticsDTO;
import com.yts.yyt.order.api.enums.OrderEnum;
import com.yts.yyt.order.api.feign.RemoteOrderPayService;
import com.yts.yyt.order.api.feign.RemoteOrderService;
import com.yts.yyt.order.api.vo.*;
import com.yts.yyt.order.handler.pay.IPaymentHandle;
import com.yts.yyt.order.service.OrderInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

import static com.yts.yyt.order.api.dto.PageMergeOrderDTO.MergeStatus.refund;
import static com.yts.yyt.order.api.dto.PageMergeOrderDTO.refundStatus;

/**
 * 订单信息
 *
 * <AUTHOR>
 * @date 2025-01-06 19:20:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pay" )
@Tag(description = "orderInfo" , name = "订单信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class OrderPayController implements RemoteOrderPayService {

    private final  OrderInfoService orderInfoService;
    public final Map<String, IPaymentHandle> paymentHandleMap;


    /**
     * 支付回调
     * @param orderPayBackDTO
     * @return
     */
    @PostMapping("/payback")
    @SysLog("支付回调")
    @Inner
    public R payback(@RequestBody OrderPayBackDTO orderPayBackDTO) {
        log.info("支付回调:{}", JSON.toJSONString(orderPayBackDTO));
        orderInfoService.handlePayback(orderPayBackDTO);
        return R.ok();
    }


    /**
     * 获取支付参数
     * @param orderId
     * @return
     */
    @GetMapping("/payParams")
    @Inner(value = false)
    public R<OrderPayParameterVO> getOrderPayParams(@RequestParam("orderId") Long orderId) {
        return R.ok(orderInfoService.getPayParams(orderId));
    }

}
