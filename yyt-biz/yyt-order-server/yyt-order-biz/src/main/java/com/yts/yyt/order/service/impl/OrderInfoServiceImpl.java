package com.yts.yyt.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.plumelog.core.TraceId;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.admin.api.feign.RemoteParamService;
import com.yts.yyt.common.alert.annotation.AlertException;
import com.yts.yyt.common.alert.enums.AlertLevel;
import com.yts.yyt.common.core.constant.ServiceNameConstants;
import com.yts.yyt.common.core.constant.TransactionSettingCodeConstants;
import com.yts.yyt.common.core.constant.UserMsgConstants;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.entity.BasePage;
import com.yts.yyt.common.core.exception.ParamsValidateException;
import com.yts.yyt.common.core.util.IDS;
import com.yts.yyt.common.data.resolver.DictResolver;
import com.yts.yyt.common.file.service.ImageUrlService;
import com.yts.yyt.common.pay.huifu.enums.HuiFuTradeTypeEnum;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.security.service.YytUser;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.goods.api.dto.GoodsLotUpdateStateDTO;
import com.yts.yyt.goods.api.dto.MerchantResellDTO;
import com.yts.yyt.goods.api.entity.GoodsLotInfo;
import com.yts.yyt.goods.api.enums.GoodsInfoEnum;
import com.yts.yyt.merchant.api.dto.CityPartnerAndMerchantDTO;
import com.yts.yyt.merchant.api.dto.MerchantDTO;
import com.yts.yyt.merchant.api.enums.MerchantAuditStateEnum;
import com.yts.yyt.merchant.api.enums.MerchantShopTypeEnum;
import com.yts.yyt.order.api.constant.OrderLogTemplate;
import com.yts.yyt.order.api.dto.*;
import com.yts.yyt.order.api.entity.*;
import com.yts.yyt.order.api.enums.*;
import com.yts.yyt.order.api.exception.OrderBizErrorCodeEnum;
import com.yts.yyt.order.api.exception.OrderException;
import com.yts.yyt.order.api.vo.*;
import com.yts.yyt.order.event.OrderStatusChangeEvent;
import com.yts.yyt.order.mapper.LogisticsCompanyMapper;
import com.yts.yyt.order.mapper.OrderInfoMapper;
import com.yts.yyt.order.mapper.OrderLogisticsMapper;
import com.yts.yyt.order.mq.dto.OrderMqDTO;
import com.yts.yyt.order.service.*;
import com.yts.yyt.order.utils.OrderNoGenerator;
import com.yts.yyt.user.api.constant.TradeDescConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.constant.enums.UserAuthenticationEnum;
import com.yts.yyt.user.api.constant.enums.UserVipTypeEnum;
import com.yts.yyt.user.api.dto.AccountTransactionSettleDTO;
import com.yts.yyt.user.api.dto.VipUpgradeDTO;
import com.yts.yyt.user.api.feign.RemoteUserMsgService;
import com.yts.yyt.user.api.vo.QueryUserAddressVO;
import com.yts.yyt.user.api.vo.UserInfoVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yts.yyt.order.api.dto.PageMergeOrderDTO.MergeStatus.refund;
import static com.yts.yyt.order.api.dto.PageMergeOrderDTO.MergeStatus.refundSaleAfter;
import static com.yts.yyt.order.api.dto.PageMergeOrderDTO.refundStatus;

@Service("orderInfoService")
@Slf4j
@AllArgsConstructor
public class OrderInfoServiceImpl extends ServiceImpl<OrderInfoMapper, OrderInfoEntity> implements OrderInfoService {

    private final OrderInfoMapper orderInfoMapper;
    private final OrderLogisticsMapper orderLogisticsMapper;
    private final RemoteParamService remoteParamService;
    private final OrderLogService orderLogService;
    private final BaseRemoteService baseRemoteService;
    private final TransactionSettingService transactionSettingService;
    private final OrderNoGenerator orderNoGenerator;
    private final LogisticsCompanyMapper logisticsCompanyMapper;
    private final RemoteUserMsgService remoteUserMsgService;
    private final LogisticsInfoService logisticsInfoService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ImageUrlService imageUrlService;
    private final OrderDelayTransService orderDelayTransService;
    private final CouponUsageService couponUsageService;
    private final UserCouponService userCouponService;
    private final AcctSplitBunchService acctSplitBunchService;
    private final WxOrderShippingService wxOrderShippingService;
    private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;
    private final ApplicationEventPublisher publisher;

    /**
     * 订单备注
     * @param orderId
     * @param remark
     */
    @Transactional
    public void remark(Long orderId, String remark) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderId);
        orderInfoEntity.setRemark(remark);
        orderInfoEntity.setUpdateTime(LocalDateTime.now());
        orderInfoMapper.updateById(orderInfoEntity);
        orderLogService.createOrderLog(orderInfoEntity, String.format(OrderLogTemplate.ORDER_PLATFORM_REMARK,remark));
    }

    @Override
    public void remove(Long orderId){
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if(ObjUtil.isEmpty(orderInfo)){
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        List<String> statusList = Lists.newArrayList(
                OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType(),
                OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType()
        );
        if(!statusList.contains(orderInfo.getStatus())){
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_STATUS_DEL_ERROR);
        }
        orderInfoMapper.deleteById(orderId);
    }

    /**
     *  生成购买订单
     * @param orderInfoDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    @AlertException(value ="生成购买订单",modules = ServiceNameConstants.ORDER_SERVER ,level = AlertLevel.CRITICAL)
    public OrderInfoVO generateOrder(OrderInfoDTO orderInfoDTO){
        log.info("生成购买订单,参数:{}", JSON.toJSONString(orderInfoDTO));
        OrderInfoVO vo = new OrderInfoVO();

        GoodsLotInfo goodInfo = baseRemoteService.getGoodsLotById(orderInfoDTO.getLotId());
        // 购买用户
        UserInfoVO userInfo = baseRemoteService.getUserInfo(orderInfoDTO.getUserId());
        // 检查参数
        checkOrderParams(orderInfoDTO, goodInfo, userInfo);
        // 生成订单
        OrderInfoEntity order = new OrderInfoEntity();
        order.setId(IDS.uniqueID());
        order.setOrderNo(orderNoGenerator.generateOrderNo());
        order.setGoodsId(goodInfo.getGoodsId());
        order.setUserId(orderInfoDTO.getUserId());
        order.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType());
        order.setOrderSource(orderInfoDTO.getOrderSource());
        order.setOrderType(orderInfoDTO.getOrderType());
        order.setIpoAmount(goodInfo.getSalePrice());
        order.setAmount(goodInfo.getSalePrice());
        order.setLogisticsCompanyId(orderInfoDTO.getLogisticsCompanyId());
        order.setLotId(orderInfoDTO.getLotId());
        order.setResellFlag(OrderEnum.ResellFlag.NO_RESELL.getType());
        order.setBuyerMessage(orderInfoDTO.getBuyerMessage());
        order.setShareCode(orderInfoDTO.getShareCode());
		order.setCreateTime(LocalDateTime.now());
        CityPartnerAndMerchantDTO cityPartnerAndMerchantDTO = baseRemoteService.getPartnerByMerchantId(goodInfo.getMerchantId());
        // 平台服务费率 (自营商家无服务费)
        BigDecimal feeValue = new BigDecimal("0");
        if(!MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(cityPartnerAndMerchantDTO.getMerchant().getShopType())) {

            feeValue = transactionSettingService.getTransactionSetting(TransactionSettingCodeConstants.CONSIGNMENT_SERVICE_FEE).getTranValue()
                    .divide(new BigDecimal(100)).setScale(4, RoundingMode.DOWN);
        }

        order.setFeeRate(feeValue);
        order.setFee((order.getAmount().multiply(feeValue)).setScale(2, RoundingMode.DOWN));

        // 超时未支付过期时间
        BigDecimal value = transactionSettingService.getTransactionSetting(TransactionSettingCodeConstants.UNPAID_AUTO_CLOSE_TIME).getTranValue();

        order.setExpirseTime(order.getCreateTime().plusMinutes(value.intValue()));
        order.setPayType(orderInfoDTO.getPayType());

        orderInfoMapper.insert(order);
        if (ObjUtil.isNotEmpty(orderInfoDTO.getUserCouponId())
                && orderInfoDTO.getUserCouponId() > 0) {
            // 根据优惠券id 获取用户优惠券，
            UserCouponEntity userCoupon = userCouponService.getById(orderInfoDTO.getUserCouponId());
            // 校验优惠券是否已使用，已过期
            checkUserCouponInfo(userCoupon, order,userInfo,cityPartnerAndMerchantDTO);
            // 用户优惠券标记已用
            userCouponService.lambdaUpdate()
                    .set(UserCouponEntity::getStatus, CouponEnum.UserCouponStatus.USED.getCode())
                    .eq(UserCouponEntity::getId, orderInfoDTO.getUserCouponId()).update();
            // 生成优惠券使用记录表 1-使用中
            CouponUsageEntity couponUsageEntity = new CouponUsageEntity();
            couponUsageEntity.setUserCouponId(orderInfoDTO.getUserCouponId());
            couponUsageEntity.setOrderId(order.getId());
            couponUsageEntity.setCouponAmount(userCoupon.getAmount());

            couponUsageEntity.setStatus(CouponEnum.UsageStatus.USING.getCode());
            couponUsageEntity.setUsageTime(LocalDateTime.now());
            couponUsageService.save(couponUsageEntity);

        }

        // 生成订单日志
        orderLogService.createOrderLog(order, String.format(OrderLogTemplate.ORDER_USER_BUY, userInfo.getUsername()));
        ;
        // 更新拍品
        GoodsLotUpdateStateDTO goodsLotUpdateStateDTO = new GoodsLotUpdateStateDTO();
        goodsLotUpdateStateDTO.setIds(Collections.singletonList(orderInfoDTO.getLotId()));
        goodsLotUpdateStateDTO.setState(GoodsInfoEnum.State.LOCKING.getCode());
        baseRemoteService.updateGoodsLotState(goodsLotUpdateStateDTO);
        // 物流信息
        OrderLogisticsEntity orderLogisticsEntity = new OrderLogisticsEntity();
        orderLogisticsEntity.setId(IDS.uniqueID());
        orderLogisticsEntity.setOrderId(order.getId());
        orderLogisticsEntity.setType(orderInfoDTO.getLogisticsType()); // 寄件方式前段传递
        if (orderInfoDTO.getAddressId() != null) {
            QueryUserAddressVO userAddress = baseRemoteService.getUserAddressById(orderInfoDTO.getAddressId());
            orderLogisticsEntity.setName(userAddress.getName());
            orderLogisticsEntity.setMobile(userAddress.getMobile());
            orderLogisticsEntity.setProvinceId(userAddress.getProvinceId());
            orderLogisticsEntity.setCityId(userAddress.getCityId());
            orderLogisticsEntity.setAreaId(userAddress.getDistrictId());
            orderLogisticsEntity.setAddress(userAddress.getFullAddress());  //调整为保存用户地址信息中fullAddress
        }
        orderLogisticsMapper.insert(orderLogisticsEntity);

        vo.setId(order.getId());
        vo.setOrderNo(order.getOrderNo());
        vo.setPayType(order.getPayType());
        return vo;
    }

    /**
     * 校验优惠券是否已使用，已过期
     * 用户等级，使用店铺范围 
     *
     * @param userCoupon
     * @param orderInfoDTO
     * @param userInfo
     * @param cityPartnerAndMerchantDTO
     */
    private void checkUserCouponInfo(UserCouponEntity userCoupon, OrderInfoEntity orderInfoDTO, UserInfoVO userInfo, CityPartnerAndMerchantDTO cityPartnerAndMerchantDTO) {
        if (ObjUtil.isEmpty(userCoupon))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_NOT_EXIST.getMsg());
        if (!CouponEnum.UserCouponStatus.UNUSED.getCode().equals(userCoupon.getStatus()))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_ALREADY_USED.getMsg());
        if (ObjUtil.isNotEmpty(userCoupon.getExpireTime())
                && LocalDateTime.now().isAfter(userCoupon.getExpireTime()))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_EXPIRED.getMsg());
        
        // 检查平台限制
        String platforms = userCoupon.getPlatform();
        if (platforms != null) {
            String orderSource = String.valueOf(orderInfoDTO.getOrderSource());
            // 检查订单来源是否在允许的平台列表中
            if (orderSource.equals(String.valueOf(OrderEnum.OrderSource.APPLET.getType())) 
                    && !platforms.contains(String.valueOf(CouponEnum.Platform.MINI_PROGRAM.getCode()))
                ) {
                throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_PLATFORM_MINI_PROGRAM_LIMIT.getMsg());
            } else if ((orderSource.equals(String.valueOf(OrderEnum.OrderSource.H5.getType())) 
                    || orderSource.equals(String.valueOf(OrderEnum.OrderSource.PC.getType())))
                    && !platforms.contains(String.valueOf(CouponEnum.Platform.APP.getCode()))) {
                throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_PLATFORM_APP_LIMIT.getMsg());
            }
        }

        if (!(orderInfoDTO.getAmount().compareTo(userCoupon.getLimitAmount()) >= 0))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_THRESHOLD_NOT_MET.getMsg());
        if (!(orderInfoDTO.getAmount().compareTo(userCoupon.getAmount()) > 0))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.GOODS_AMOUNT_GREATER_THAN_COUPON.getMsg());
        if (userInfo.getBuyerVipLevel() < userCoupon.getLevelScope())
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_NOT_MEET_MEMBER_THRESHOLD.getMsg());
        String shopType = cityPartnerAndMerchantDTO.getMerchant().getShopType(); // 店铺类型
        String shopScope = userCoupon.getShopScope(); // 店铺范围
        // 自营店铺
        if ((CouponEnum.ShopScope.MERCHANT.getCode().equals(shopScope)
                && !MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(shopType)
        ) || (CouponEnum.ShopScope.SHOP.getCode().equals(shopScope)
                && MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(shopType)
        )
        ) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_NOT_SUPPORTED_BY_STORE.getMsg());

        }
        // 判断优惠券是否属于当前购买人(登录人)
        if (!userCoupon.getUserId().equals(orderInfoDTO.getUserId()))
            throw new ParamsValidateException(OrderBizErrorCodeEnum.COUPON_NOT_BELONG_TO_CURRENT_BUYER.getMsg());

    }

    /**
     * 支付三方回调
     * @param orderPayBackDTO
     * @return
     */
    @Transactional
    @Lock4j(keys = {"#orderPayBackDTO.orderId"})
    @GlobalTransactional(rollbackFor = Exception.class)
    @AlertException(value ="支付三方回调",modules = ServiceNameConstants.ORDER_SERVER ,level = AlertLevel.CRITICAL)
    public Boolean handlePayback(OrderPayBackDTO orderPayBackDTO) {
        OrderInfoEntity orderInfoEntity = orderInfoMapper.selectById(orderPayBackDTO.getOrderId());
        if (orderInfoEntity == null) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.FIRST_LAUNCH_NOT_FOUND.getMsg());
        }
        if (orderInfoEntity.getPayStatus() != null && orderPayBackDTO.getPayStatus() != OrderEnum.OrderPayStatus.SUCCESS.getType()){
            log.info("支付回调，支付状态不成功，{}" , JSON.toJSONString(orderPayBackDTO));
            // 非支付成功状态 不处理
            return false;
        }
        if (!orderInfoEntity.getStatus().equals(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType())) {
            throw OrderException.build(OrderBizErrorCodeEnum.FIRST_LAUNCH_STATUS_MISMATCH);
        }

        orderInfoEntity.setPayType(HuiFuTradeTypeEnum.getPayTypeByType(orderPayBackDTO.getPayType()));
        orderInfoEntity.setPaySerio(orderPayBackDTO.getPaySerio());
        orderInfoEntity.setPayTime(LocalDateTime.now());
        orderInfoEntity.setPayStatus(orderPayBackDTO.getPayStatus());
        orderInfoEntity.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_DELIVERY.getType());
        orderInfoEntity.setOutTransId(orderPayBackDTO.getOutTransId());
        orderInfoEntity.setPartyOrderId(orderPayBackDTO.getPartyOrderId());
        orderInfoMapper.updateById(orderInfoEntity);
        UserInfoVO info = baseRemoteService.getUserInfo(orderInfoEntity.getUserId());
        orderLogService.createOrderLog(orderInfoEntity,String.format( OrderLogTemplate.ORDER_USER_PAY,info.getUsername()));

        // 支付成功 升级用户等级（买家）
		CouponUsageEntity couponUsage = couponUsageService.lambdaQuery()
				.eq(CouponUsageEntity::getOrderId, orderInfoEntity.getId())
				.eq(CouponUsageEntity::getStatus, CouponEnum.UsageStatus.USING.getCode())
				.one();
        VipUpgradeDTO vipUpgradeDTO = new VipUpgradeDTO();
        vipUpgradeDTO.setUserId(orderInfoEntity.getUserId());
        vipUpgradeDTO.setVipType(UserVipTypeEnum.BUYER.TYPE);
        vipUpgradeDTO.setAmount(couponUsage != null ? orderInfoEntity.getAmount().subtract(couponUsage.getCouponAmount()) : orderInfoEntity.getAmount());
        vipUpgradeDTO.setBizNo(orderInfoEntity.getOrderNo());
        baseRemoteService.upgradeVipLevel(vipUpgradeDTO);

        // 修改商品状态
        GoodsLotUpdateStateDTO goodsLotUpdateStateDTO = new GoodsLotUpdateStateDTO();
        goodsLotUpdateStateDTO.setIds(Collections.singletonList(orderInfoEntity.getLotId()));
        goodsLotUpdateStateDTO.setState(GoodsInfoEnum.State.SOLD.getCode());
        goodsLotUpdateStateDTO.setOrderNo(orderInfoEntity.getOrderNo()); // 更新订单编号
        baseRemoteService.updateGoodsLotState(goodsLotUpdateStateDTO);

        // 订单商户信息
        OrderMerchantVO merchantInfo = orderInfoMapper.getOrderMerchantInfo(orderInfoEntity.getId());
        // 创建延迟支付订单
        OrderDelayTransEntity orderDelayTrans = new OrderDelayTransEntity();
        orderDelayTrans.setOrderId(orderInfoEntity.getId())
                .setUserId(merchantInfo.getUserId())
                .setStatus(OrderEnum.OrderDelayStatus.DELAY_WAIT_PAYMENT.getStatue())
                .setAmount(orderInfoEntity.getAmount().subtract(orderInfoEntity.getFee()))
                .setDelayAmount(couponUsage == null ?
                        orderInfoEntity.getAmount().subtract(orderInfoEntity.getFee()) :
                        orderInfoEntity.getAmount().subtract(orderInfoEntity.getFee().subtract(couponUsage.getCouponAmount())))
                .setPaySerio(orderInfoEntity.getPaySerio())
                .setOrgReqDate(orderPayBackDTO.getOrgReqDate())
                .setOrgReqSeqId(orderPayBackDTO.getOrgReqSeqId());

        orderDelayTransService.save(orderDelayTrans);

        // 保存分账数据
        if (ObjUtil.isNotEmpty(orderPayBackDTO.getAcctSplitBunches())) {
            for (AcctSplitBunchEntity entity : orderPayBackDTO.getAcctSplitBunches()) {
                entity.setOrderId(orderInfoEntity.getId());
            }
            acctSplitBunchService.saveBatch(orderPayBackDTO.getAcctSplitBunches());
        }

        // 操作用户账户
        settleTransaction(orderInfoEntity,merchantInfo.getUserId(),merchantInfo.getShopType());

        // 支付成功  优惠券使用记录 状态：2-已核销
        couponUsageService.lambdaUpdate()
                .set(CouponUsageEntity::getStatus,CouponEnum.UsageStatus.VERIFIED.getCode())
                .eq(CouponUsageEntity::getOrderId,orderInfoEntity.getId()).update();

        GoodsLotInfo goodsInfoVO = baseRemoteService.getGoodsLotById(orderInfoEntity.getLotId());

        // 生成微信小程序发货 3-草稿待处理数据
        if (HuiFuTradeTypeEnum.T_MINIAPP.getType().equals(orderPayBackDTO.getPayType())) {
            WxOrderShippingEntity wxOrderShipping = new WxOrderShippingEntity();
            wxOrderShipping.setBusinessId(orderInfoEntity.getId());
            wxOrderShipping.setOrderNumberType(2);
            wxOrderShipping.setTransactionId(orderInfoEntity.getOutTransId());
            wxOrderShipping.setLogisticsType(1);
            wxOrderShipping.setDeliveryMode(1);
            wxOrderShipping.setItemDesc(goodsInfoVO.getName());
            wxOrderShipping.setOpenid(orderPayBackDTO.getWeAppOpenid());
            wxOrderShipping.setStatus(WxOrderEnum.ShippingStatus.DRAFT_PENDING.getCode()); // 3-草稿待处理数据
            try{
                wxOrderShippingService.save(wxOrderShipping);
                log.info("生成微信小程序发货 3-草稿待处理数据 :{}" , JSON.toJSONString(wxOrderShipping));
            }catch (Exception e){
                // 发货异常不用处理，有补偿机制
                log.error("小程序订单发货，3-草稿待处理数据，参数:{}" , JSON.toJSONString(wxOrderShipping));
                log.error(e.getMessage(),e);
            }
            
        }
        
        // 订单支付成功通知
        Map<String,Object> map = new HashMap<>();
        map.put("userId",orderInfoEntity.getUserId());
        map.put("msgScene", UserMsgConstants.ORDER_PAYMENT_NOTIFICATION);
        map.put("payTime",orderInfoEntity.getCreateTime());
        map.put("orderId",orderInfoEntity.getOrderNo());
        map.put("goodsName",goodsInfoVO.getName());
        map.put("orderAmount",orderInfoEntity.getAmount());

        // 卖家消息 todo
        // 买家支付通知
        remoteUserMsgService.sendUserMsg(map);
        if(!ObjectUtil.isEmpty(orderInfoEntity.getShareCode())) {
            // 实付金额
            BigDecimal amount = couponUsage == null ?
                    orderInfoEntity.getAmount() :
                    orderInfoEntity.getAmount().subtract(couponUsage.getCouponAmount());
            orderPaySendMqMessage(orderInfoEntity,amount,goodsInfoVO,merchantInfo.getMerchantId());
        }
        return true;
    }


    private void checkOrderParams(OrderInfoDTO orderInfoDTO,GoodsLotInfo goodInfo,UserInfoVO userInfo) {
        if(!OrderEnum.OrderType.contains(orderInfoDTO.getOrderType())) {
            throw new ParamsValidateException("订单类型不存在");
        }
        if(!OrderEnum.OrderSource.contains(orderInfoDTO.getOrderSource())) {
            throw new ParamsValidateException("订单来源不存在");
        }
        if(!OrderEnum.OrderPayType.contains(orderInfoDTO.getPayType())) {
            throw new ParamsValidateException("支付方式不存在");
        }
        if(!OrderLogisticsEnum.contains(orderInfoDTO.getLogisticsType())) {
            throw new ParamsValidateException("寄件方式不存在");
        }

        if (goodInfo == null){
            throw new ParamsValidateException(OrderBizErrorCodeEnum.FIRST_LAUNCH_GOOD_NOT_FOUND.getMsg());
        }
        // 商品状态
        if (!goodInfo.getState().equals(GoodsInfoEnum.State.SELLING.getCode())){
            throw new ParamsValidateException(OrderBizErrorCodeEnum.FIRST_LAUNCH_GOOD_NOT_SEALING.getMsg());
        }
        // 下单购买需实名认证金额
        String BUY_AUTH_AMOUNT = remoteParamService.getByKey(OrderParamEnum.BUY_AUTH_AMOUNT.getKey()).getData();
        if (StrUtil.isBlank(BUY_AUTH_AMOUNT))
            BUY_AUTH_AMOUNT = "50000"; // 默认5万
        // 4.24 若≥50000，需实名认证
        // 购买订单需完成身份认证
        BigDecimal buyAmount = new BigDecimal(BUY_AUTH_AMOUNT);
        if(goodInfo.getSalePrice().compareTo(buyAmount) > 0){
            if(!userInfo.getIsIdAuth()) {
                throw new ParamsValidateException(OrderBizErrorCodeEnum.IDENTITY_AUTH_NOT_COMPLETED.getMsg());
            }
        }
        if (OrderEnum.OrderType.STORE.getType().equals(orderInfoDTO.getOrderType())
                && ObjectUtil.isEmpty(orderInfoDTO.getAddressId())) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.ADDRESS_IS_NULL.getMsg());
        }
        // 卖家应不可购买自己的店铺的拍品
        if (ObjectUtil.isNotEmpty(userInfo.getMerchantId()) &&
                goodInfo.getMerchantId().equals(userInfo.getMerchantId())) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.NO_BUY_OWN_GOODS.getMsg());
        }
    }

    private void settleTransaction(OrderInfoEntity info,Long userId,String shopType){
        // 支付成功+用户余额（冻结）
        AccountTransactionSettleDTO transactionSettleDTO = new AccountTransactionSettleDTO();
        transactionSettleDTO.setUserId(userId);
        transactionSettleDTO.setAccountType(AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
        transactionSettleDTO.setAmount(info.getAmount());
        transactionSettleDTO.setFee(MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(shopType) ? new BigDecimal("0.00") : info.getFee());
        transactionSettleDTO.setBusinessId(String.valueOf(info.getId()));
        transactionSettleDTO.setTradeDesc(TradeDescConstants.ORDER_PAYMENT_SUCCESS);
        transactionSettleDTO.setFeeTradeDesc(TradeDescConstants.TRANSACTION_FEE);
        baseRemoteService.settleTransaction(transactionSettleDTO);
    }

    /**
     * 支付成功发送消息队列
     * @param order
     * @param lotInfo
     * @param merchantId
     */
    private void orderPaySendMqMessage(OrderInfoEntity order,BigDecimal amount,GoodsLotInfo lotInfo,Long merchantId){
        OrderMqDTO dto = new OrderMqDTO();
        dto.setOrderId(order.getId())
                .setStatus(order.getStatus())
                .setAmount(amount)
                .setPayTime(order.getPayTime())
                .setOrderSource(order.getOrderSource())
                .setPayType(order.getPayType())
                .setShareCode(order.getShareCode())
                .setMainImg(lotInfo.getMainImage())
                .setGoodsName(lotInfo.getName())
                .setOrderNo(order.getOrderNo())
                .setMerchantId(merchantId)
                .setBuyUserId(order.getUserId());

        Message<OrderMqDTO> message = MessageBuilder
                .withPayload(dto)
                .build();
        envAwareRocketMQTemplate.convertAndSend(
                RocketMQConstants.Topic.TOPIC_ORDER_STATUS_CHANGED,message);
        log.info("消息发送成功 dto: {}", JSON.toJSONString(dto));
    }

    /**
     * 获取支付参数
     * @param orderId
     * @return
     */
    @Override
    @AlertException(value ="获取支付参数",modules = ServiceNameConstants.ORDER_SERVER ,level = AlertLevel.CRITICAL)
    public OrderPayParameterVO getPayParams(Long orderId) {
        OrderPayParameterVO vo = orderInfoMapper.getOrderPayParams(orderId);
        if(vo == null) {
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        String FEE_FLAG = remoteParamService.getByKey(OrderParamEnum.FEE_FLAG.getKey()).getData();
        vo.setFeeFlag(FEE_FLAG);
        if(ObjectUtil.isEmpty(vo.getFee())) {
            // 平台服务费率
            BigDecimal feeValue = transactionSettingService.getTransactionSetting(TransactionSettingCodeConstants.CONSIGNMENT_SERVICE_FEE).getTranValue()
                    .divide(new BigDecimal(100)).setScale(4, RoundingMode.DOWN);

            vo.setFeeRate(feeValue);
            vo.setFee((vo.getAmount().multiply(vo.getFeeRate())).setScale(2, RoundingMode.DOWN));

        }
        // 优惠券金额处理
        if(ObjectUtil.isNotEmpty(vo.getCouponAmount())) {
            // 用户实际支付金额 = 订单金额   - 优惠券金额
            vo.setAmount(vo.getAmount().subtract(vo.getCouponAmount()));
            // 平台实收服务费 = 用户实际支付金额  * 平台服务费率（%）
            vo.setFee((vo.getAmount().multiply(vo.getFeeRate())).setScale(2, RoundingMode.DOWN));
        }

        UserInfoVO userInfo = baseRemoteService.getUserInfo(vo.getUserId());
        vo.setWxSubOpenid(userInfo.getWeappOpenid());
        vo.setDelayAcctFlag("Y");
        vo.setAcctSplitBunchFlag("Y");
        vo.setOrderType(OrderPayParameterVO.ORDER_TYPE);
        // 分账对象
        vo.setAcctSplitBunches(getAcctInfos(vo));

        return vo;
    }

    @Transactional
    public List<OrderInfoEntity> getTimeoutOrderList(){
        LambdaQueryWrapper<OrderInfoEntity> queryWrapper = new QueryWrapper<OrderInfoEntity>().lambda();
        queryWrapper.eq(OrderInfoEntity::getStatus, OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType()).
                lt(OrderInfoEntity::getExpirseTime, LocalDateTime.now());
        return orderInfoMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public void orderTimeoutCancel(OrderInfoEntity order){
        log.info("订单超时任务开始,orderID:{}" , order.getId());
        order.setStatus(OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType());
        orderInfoMapper.updateById(order);
        // 生成订单日志
        orderLogService.createOrderLog(order, OrderLogTemplate.ORDER_EXPIRED_CLOSED);
        GoodsLotUpdateStateDTO goodsLotUpdateStateDTO = new GoodsLotUpdateStateDTO();
        goodsLotUpdateStateDTO.setIds(Collections.singletonList(order.getLotId()));
        goodsLotUpdateStateDTO.setState(GoodsInfoEnum.State.SELLING.getCode());
        baseRemoteService.updateGoodsLotState(goodsLotUpdateStateDTO);

        // 订单关闭通知
        OrderDetailVO orderDetailVO = orderDetail(order.getId());
        // 	超时未支付 退还优惠券 
        // 	优惠券使用记录 状态：4-订单已关闭
        // 用户优惠券  状态：1-未用
        couponUsageService.updateCouponUsageStatus(order.getId(),
                CouponEnum.UsageStatus.CLOSED.getCode(),
                CouponEnum.UserCouponStatus.UNUSED.getCode());

        if(ObjUtil.isNotEmpty(orderDetailVO)){
            Map<String,Object> map = new HashMap<>();
            map.put("userId",order.getUserId());
            map.put("msgScene", UserMsgConstants.ORDER_CLOSE_NOTIFICATION);
            map.put("orderId",order.getOrderNo());
            map.put("goodsName",orderDetailVO.getGoodsName());
            remoteUserMsgService.sendUserMsg(map);
        }
        log.info("订单超时任务结束,orderID:{}" , order.getId());
    }

    private String getSearchColumn(String searchType){
        if(OrderSearchEnum.ORDER_NO.getCode().equals(searchType)){
            return "a.order_no";
        }else if(OrderSearchEnum.GOODS_NAME.getCode().equals(searchType)){
            return "b.goods_name";
        }else if(OrderSearchEnum.GOODS_CODE.getCode().equals(searchType)){
            return "b.goods_code";
        } else if (OrderSearchEnum.ORDER_REMARK.getCode().equals(searchType)) {
            return "a.remark";
        } else if (OrderSearchEnum.USERNAME.getCode().equals(searchType)) {
            return "c.username";
        }
        return null;
    }

    public Page<PageOrderVO> appPage(Page page, PageOrderDTO dto) {
        QueryWrapper<ConsignmentOrderVO> wrapper = new QueryWrapper<>();
        YytUser yytUser = SecurityUtils.getUser();
        Long userId = Objects.requireNonNull(yytUser).getId();

        wrapper.eq("a.del_flag", 0);
        wrapper.eq("a.user_id", userId);
        Page<PageOrderVO> pageOrderVOPage = this.getPageOrderVOPage(page, dto, wrapper);
        return pageOrderVOPage;
    }

    public Page<PageOrderVO> getPageOrderVOPage(Page page, PageOrderDTO dto, QueryWrapper<ConsignmentOrderVO> wrapper) {
        if(CollectionUtils.isNotEmpty(dto.getOrderType())){
            wrapper.in("a.order_type",dto.getOrderType());
        }
        if (CollectionUtils.isNotEmpty(dto.getOrderStatus()) && CollectionUtils.isNotEmpty(dto.getProtectStatus())) {
            wrapper.and(w->w.in("a.status",dto.getOrderStatus()).or().in("a.protect_status",dto.getProtectStatus()));
        }else{
            if(CollectionUtils.isNotEmpty(dto.getOrderStatus())){
                wrapper.in("a.status",dto.getOrderStatus());
            }
            if(CollectionUtils.isNotEmpty(dto.getProtectStatus())){
                wrapper.in("a.protect_status",dto.getProtectStatus());
                wrapper.ne("a.status", OrderEnum.OrderStatus.FIRST_LAUNCH_CLOSED.getType());
            }
        }
        if(StringUtils.isNotBlank(dto.getGoodsName())){
            wrapper.like("b.name", dto.getGoodsName());
        }
        if(StringUtils.isNotBlank(dto.getCreateTimeStart())){
            dto.setCreateTimeStart(dto.getCreateTimeStart()+" 00:00:00");
            wrapper.ge("a.create_time", dto.getCreateTimeStart());
        }
        if(StringUtils.isNotBlank(dto.getCreateTimeEnd())){
            dto.setCreateTimeEnd(dto.getCreateTimeEnd()+" 23:59:59");
            wrapper.le("a.create_time", dto.getCreateTimeEnd());
        }
        if(StringUtils.isNotBlank(dto.getAmountMin())){
            wrapper.ge("a.amount", dto.getAmountMin());
        }
        if(StringUtils.isNotBlank(dto.getAmountMax())){
            wrapper.le("a.amount", dto.getAmountMax());
        }
        wrapper.orderByDesc("a.create_time");

        Page<PageOrderVO> pageOrderVOPage = orderInfoMapper.appPage(page, wrapper);
        //物流方式信息
        if (CollectionUtil.isNotEmpty(pageOrderVOPage.getRecords())) {
            List<Long> orderIds = pageOrderVOPage.getRecords().stream().map(PageOrderVO::getId).collect(Collectors.toList());
            Map<Long, OrderLogisticsEntity> logiMap = orderLogisticsMapper.selectList(new LambdaQueryWrapper<OrderLogisticsEntity>().in(OrderLogisticsEntity::getOrderId, orderIds))
                    .parallelStream().collect(Collectors.toMap(OrderLogisticsEntity::getOrderId, item -> item, (v1, v2) -> v1));
            for (PageOrderVO record : pageOrderVOPage.getRecords()) {
                record.setLogisticsType(logiMap.containsKey(record.getId()) ? logiMap.get(record.getId()).getType()+"" : null);
            }
        }
        return pageOrderVOPage;
    }

    /**
     * 小程序:订单详情
     * @param orderId
     * @return
     */
    public OrderDetailVO orderDetail(Long orderId) {

        QueryWrapper<ConsignmentOrderVO> wrapper = new QueryWrapper<>();
        YytUser yytUser = SecurityUtils.getUser();
        if(ObjUtil.isNotEmpty(yytUser)){
            Long userId = Objects.requireNonNull(yytUser).getId();
            wrapper.eq("a.user_id", userId);
        }
        OrderDetailVO orderDetailVO = this.getOrderDetailVO(orderId, wrapper);
        return orderDetailVO;
    }

    /**
     * 订单详情
     * @param orderId
     * @param wrapper
     * @return
     */
    private OrderDetailVO getOrderDetailVO(Long orderId, QueryWrapper<ConsignmentOrderVO> wrapper) {
        wrapper.eq("a.id", orderId);

        Page<PageOrderVO> result = orderInfoMapper.appPage(new Page(1, 1), wrapper);
        if (result == null || CollectionUtils.isEmpty(result.getRecords())){
            return null;
        }

        LambdaQueryWrapper<OrderLogisticsEntity> queryWrapper = new QueryWrapper<OrderLogisticsEntity>().lambda();
        queryWrapper.eq(OrderLogisticsEntity::getOrderId, orderId);
        queryWrapper.orderByDesc(OrderLogisticsEntity::getCreateTime).last(" limit 1 ");
        OrderLogisticsEntity logistics = orderLogisticsMapper.selectOne(queryWrapper);

        PageOrderVO pageOrderVO = result.getRecords().get(0);
        OrderDetailVO orderDetailVO = new OrderDetailVO();

        // DC 2025/3/27 17:53 处理用户寄件信息
        if(pageOrderVO.getRefundAddressId() != null){
            QueryUserAddressVO userAddress = logisticsInfoService.getOrderLogisticsAdress(pageOrderVO.getRefundLogisticsNumber(), pageOrderVO.getRefundId());
            if(userAddress != null){
                userAddress.setId(pageOrderVO.getRefundAddressId());
            }
            pageOrderVO.setUserRefundAddress(JSONUtil.toJsonStr(userAddress));
        }

        BeanUtil.copyProperties(pageOrderVO, orderDetailVO);
        orderDetailVO.setLogistics(logistics);

        LambdaQueryWrapper<LogisticsCompanyEntity> queryWrapper1 = new QueryWrapper<LogisticsCompanyEntity>().lambda();
        queryWrapper1.eq(LogisticsCompanyEntity::getId, orderDetailVO.getLogisticsCompanyId());
        queryWrapper1.orderByDesc(LogisticsCompanyEntity::getCreateTime).last(" limit 1 ");
        LogisticsCompanyEntity logisticsCompany = logisticsCompanyMapper.selectOne(queryWrapper1);

        orderDetailVO.setLogisticsCompany(logisticsCompany);

        // 转换退款凭证图片列表
        if(StrUtil.isNotBlank(pageOrderVO.getRefundVoucherImgs())){
            orderDetailVO.setRefundVoucherImgList(JSONUtil.toList(pageOrderVO.getRefundVoucherImgs(),String.class));
        }
        // 主图第一张： _ms
        orderDetailVO.setImgUrl(imageUrlService.convertImageUrl(orderDetailVO.getMainImage()).getSmallWebpUrl());
        orderDetailVO.setMainImage(imageUrlService.convertImageUrl(orderDetailVO.getMainImage()).getOriginUrl());
        return orderDetailVO;
    }


    /**
     * 合并订单状态的数量
     * @param listCount
     * @param status
     * @return
     */
    private Long countBuild(List<Map<String, Object>> listCount, List<String> status){
        Long countResult = 0L;
        for (Map<String, Object> objectMap : listCount) {
            String statusVal = (String) objectMap.get("statusVal");
            if(status.contains(statusVal)){
                countResult += (Long) objectMap.get("quantity");
            }
        }
        return countResult;
    }
    

    /**
     * 查询物流，签收状态；
     * 若 物流已签收等待24H后，系统确认收货
     * 并更新物流信息
     */
    public void delOrderlogistics(){
        // 查询签收物流后自动确认收货配置
        BigDecimal day = transactionSettingService
                .getTransactionSetting(TransactionSettingCodeConstants.AUTO_COMPLETE_TIME).getTranValue();
        
        List<SysDictItem> dictItem = DictResolver.getDictItemsByType(OrderParamEnum.KUAIDI100_POLL_QUERY_STATE.getKey());
        // 查询待收货订单
        LambdaQueryWrapper<OrderInfoEntity> queryWrapper = new QueryWrapper<OrderInfoEntity>().lambda();
        queryWrapper.in(OrderInfoEntity::getStatus,
                        Arrays.asList(OrderEnum.OrderStatus.FIRST_LAUNCH_SHIPPED.getType(),
                                OrderEnum.OrderStatus.CONSIGNMENT_WAIT_USER_RECEIVE.getType()))
                .ge(OrderInfoEntity::getAutoReceiveTime, LocalDateTime.now().plusDays(day.longValue()))
                .isNotNull(OrderInfoEntity::getLogisticsNumber)
                .orderByDesc(OrderInfoEntity::getUpdateTime);
        List<OrderInfoEntity> orderInfoEntities = orderInfoMapper.selectList(queryWrapper);
        if (orderInfoEntities.size() == 0)
            return ;
        List<Long> orderIds = Lists.newArrayList();
        for (OrderInfoEntity vo : orderInfoEntities) {
            orderIds.add(vo.getId());
        }
        //查询物流单号
        List<OrderLogisticsEntity> logisticsEntityList =
                new LambdaQueryChainWrapper<>(orderLogisticsMapper)
                        .in(CollUtil.isNotEmpty(orderIds), OrderLogisticsEntity::getOrderId, orderIds)
                        .isNotNull(OrderLogisticsEntity::getLogisticsNumber)
                        .orderByDesc(OrderLogisticsEntity::getId)
                        .list();
        if (CollUtil.isNotEmpty(logisticsEntityList)) {
            Map<Long, String> logisticsMap = logisticsEntityList
                    .stream()
                    .filter(entity -> entity.getOrderId() != null && entity.getLogisticsNumber() != null)
                    .collect(Collectors.toMap(OrderLogisticsEntity::getOrderId, OrderLogisticsEntity::getLogisticsNumber, (v1, v2) -> v1));
            if (CollUtil.isNotEmpty(logisticsMap)) {
                for (OrderInfoEntity record : orderInfoEntities) {
                    record.setLogisticsNumber(logisticsMap.get(record.getId()));
                }
            }
        }
        // 查询订单物流状态
        for (OrderInfoEntity record : orderInfoEntities) {
            try {
                String data = logisticsInfoService.queryDetail(record.getLogisticsNumber(), String.valueOf(record.getId()));
                // 校验订单物流是否已签收
                cn.hutool.json.JSONObject response = JSONUtil.parseObj(data);
                String state = String.valueOf(response.get("state"));
                // 是否签收标记，0未签收，1已签收，请忽略，明细状态请参考state字段
                String ischeck = String.valueOf(response.get("ischeck"));
                boolean upState = false;
                if (ObjUtil.isNotEmpty(dictItem) && StrUtil.isNotEmpty(state))
                    upState = dictItem.stream().anyMatch(sysDictItem -> sysDictItem.getItemValue().equals(state));
                if (StrUtil.isNotEmpty(ischeck) && ischeck.equals("1")) {
                    record.setAutoReceiveTime(LocalDateTime.now().plusDays(day.longValue()));
                    orderInfoMapper.updateById(record);
                }
                // 更新获取到的物流信息
                logisticsInfoService.lambdaUpdate()
                        .set(LogisticsInfoEntity::getInfo,data)
                        .set(LogisticsInfoEntity::getLastUpdate,LocalDateTime.now())
                        .eq(LogisticsInfoEntity::getId, record.getLogisticsNumber())
                        .update();
            } catch (Exception e) {
                log.error("物流已签收系统确认收货处理失败", e);
                log.info("物流已签收系统确认收货处理失败订单号：{}", record.getId());
            }
        }
    }

    /**
     * 功能 订单催发货
     * 创建于 2025/3/28 15:23
     * <AUTHOR>
     * @param orderId 订单ID
     * @return Boolean
     */
    public Boolean orderUrgeNum(Long orderId) {
        // DC 2025/3/28 14:58 缓存检验12小时只能催发货一次
        String redisKey = "ORDER_URGE_NUM_REDIS:"+orderId;
        String resultVal = stringRedisTemplate.opsForValue().get(redisKey);
        if(StrUtil.isNotEmpty(resultVal)){
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_URGE_NUM_ERROR);
        }

        OrderInfoEntity orderInfo = orderInfoMapper.selectById(orderId);
        if(ObjUtil.isEmpty(orderInfo)){
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        OrderInfoEntity updateInfo = new OrderInfoEntity();
        updateInfo.setId(orderId);
        updateInfo.setUrgeNum(orderInfo.getUrgeNum() + 1);
        boolean flag = orderInfoMapper.updateById(updateInfo) > 0;

        if(flag){
            stringRedisTemplate.opsForValue().set(redisKey, orderId+"", 12*3600, TimeUnit.SECONDS);
        }
        return flag;
    }

    @AlertException(value ="客户端:订单修改地址",modules = ServiceNameConstants.ORDER_SERVER ,level = AlertLevel.WARN)
    public Boolean updateAddress(OrderInfoUpdateAddressDTO dto) {
        // 1. 查询订单
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(dto.getOrderId());
        if (orderInfo == null) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.ORDER_NOT_EXIST.getMsg());
        }
        if (!(orderInfo.getStatus().equals(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_DELIVERY.getType())
               || orderInfo.getStatus().equals(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType()))

        ) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.ORDER_IS_DELIVERY.getMsg());
        }

        OrderLogisticsEntity orderLogisticsEntity = orderLogisticsMapper.getByOrderId(dto.getOrderId() + "");
        if(ObjectUtil.isNotNull(orderLogisticsEntity)){
            QueryUserAddressVO userAddress = baseRemoteService.getUserAddressById(dto.getAddressId());
            orderLogisticsEntity.setName(userAddress.getName())
                    .setMobile(userAddress.getMobile())
                    .setProvinceId(userAddress.getProvinceId())
                    .setCityId(userAddress.getCityId())
                    .setAreaId(userAddress.getDistrictId())
                    .setAddress(userAddress.getFullAddress())
                    .setUpdateTime(LocalDateTime.now());
        }
        return orderLogisticsMapper.updateById(orderLogisticsEntity) > 0;
    }

    /**
     * 修改订单价格
     * @param firstLaunchOrderDTO
     * @return
     */
    @Transactional
    @AlertException(value ="修改订单价格-卖家",modules = ServiceNameConstants.ORDER_SERVER ,level = AlertLevel.WARN)
    public Boolean orderUpdatePrice(GoodsLotUpdatePriceDto firstLaunchOrderDTO) {
        OrderInfoEntity orderInfo = orderInfoMapper.selectById(firstLaunchOrderDTO.getOrderId());
        CouponUsageEntity couponUsage = couponUsageService.lambdaQuery()
                .eq(CouponUsageEntity::getOrderId, firstLaunchOrderDTO.getOrderId())
                .one();
        if(ObjUtil.isEmpty(orderInfo)){
            throw new ParamsValidateException(OrderBizErrorCodeEnum.ORDER_NOT_EXIST.getMsg());
        }
        if (!orderInfo.getStatus().equals(OrderEnum.OrderStatus.FIRST_LAUNCH_WAITING_PAYMENT.getType())) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.FIRST_LAUNCH_STATUS_MISMATCH.getMsg());
        }

        BigDecimal limitAmount = new BigDecimal(0.10).setScale(2, RoundingMode.DOWN);
        if (ObjUtil.isNotEmpty(couponUsage)
                && !((firstLaunchOrderDTO.getSalePrice().subtract(couponUsage.getCouponAmount())).compareTo(limitAmount) >= 0)) {
            throw new ParamsValidateException(OrderBizErrorCodeEnum.UPDATE_AMOUNT_GREATER_THAN_COUPON.getMsg());
        }
        orderInfo.setAmount(firstLaunchOrderDTO.getSalePrice());
        orderInfo.setIpoAmount(firstLaunchOrderDTO.getSalePrice());
        GoodsLotInfo goodsLotInfo = baseRemoteService.getGoodsLotById(orderInfo.getLotId());
        CityPartnerAndMerchantDTO cityPartnerAndMerchantDTO =  baseRemoteService.getPartnerByMerchantId(goodsLotInfo.getMerchantId());
        // 平台服务费率
        if(!MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(cityPartnerAndMerchantDTO.getMerchant().getShopType())) {
            BigDecimal feeValue = transactionSettingService.getTransactionSetting(TransactionSettingCodeConstants.CONSIGNMENT_SERVICE_FEE)
                    .getTranValue().divide(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP);

            orderInfo.setFee((orderInfo.getIpoAmount().multiply(feeValue)).setScale(2, RoundingMode.DOWN));
        }
        // 生成订单日志
        orderLogService.createOrderLog(orderInfo, OrderLogTemplate.ORDER_UPDATE_PRICE);
        return orderInfoMapper.updateById(orderInfo) > 0;
    }

    /**
     * 获取我的藏品列表
     * @param basePage
     * @return
     */
    public Page<PageStoreOrderVO> userGoodsOrderPage(BasePage basePage) {
        Page page = new Page(basePage.getCurrent(),basePage.getSize());
        YytUser yytUser = SecurityUtils.getUser();
        Long userId = Objects.requireNonNull(yytUser).getId();
        Page<PageStoreOrderVO> pageStoreOrderVOPage =
                orderInfoMapper.userGoodsOrderPage(page,userId);
        pageStoreOrderVOPage.getRecords().parallelStream().forEach(storeOrderVO -> {
            if (StrUtil.isBlank(storeOrderVO.getMainImage())) {
                return;
            }
            // 主图第一张： _ms
            storeOrderVO.setImgUrl((imageUrlService.convertImageUrl(storeOrderVO.getMainImage()).getSmallWebpUrl()));
            storeOrderVO.setMainImage(imageUrlService.convertImageUrl(storeOrderVO.getMainImage()).getOriginUrl());

        });
        return pageStoreOrderVOPage;
    }

    /**
     * 客户端转售
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public Boolean resell(MerchantResellDTO dto) {
        LambdaQueryWrapper<OrderInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 前端传 lotId = 订单id
        queryWrapper.eq(OrderInfoEntity::getId, dto.getLotId());
        OrderInfoEntity orderInfo = orderInfoMapper.selectOne(queryWrapper);
        if(ObjUtil.isEmpty(orderInfo)){
            throw OrderException.build(OrderBizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        if(OrderEnum.ResellFlag.IS_RESELL.getType().equals(orderInfo.getResellFlag())){
            throw OrderException.build(OrderBizErrorCodeEnum.IS_RESELL_ERROR);
        }
        YytUser yytUser = SecurityUtils.getUser();
        Long userId = Objects.requireNonNull(yytUser).getId();
        validateMerchantAuditStateAndAuth(userId);
        dto.setUserId(userId);
        dto.setAmount(orderInfo.getAmount());
        dto.setLotId(orderInfo.getLotId());
        baseRemoteService.resell(dto);
        // 更新转售标识

        orderInfo.setResellFlag(OrderEnum.ResellFlag.IS_RESELL.getType());
        return orderInfoMapper.updateById(orderInfo) > 0;

    }

    /**
     * 校验商家入驻审核状态及实名认证
     * @param userId 用户id
     */
    private void validateMerchantAuditStateAndAuth(Long userId) {
        //校验商家审核状态
        MerchantDTO merchant = baseRemoteService.getMerchantByUserId(userId);
        if (!MerchantAuditStateEnum.APPROVED.getState().equals(merchant.getAuditState())) {
            log.info("【GoodsLotInfoServiceImpl.resell】 转售失败，商家审核状态不是已通过状态：{}", merchant.getAuditState());
            throw OrderException.build(OrderBizErrorCodeEnum.MERCHANT_NOT_APPROVED);
        }
        // 个人商家入驻时,有可能没有实名认证 
        if(merchant.getShopType().equals(MerchantShopTypeEnum.PERSONAL_SHOP.getType())){
            // 实名认证
            Boolean userAuthentication = baseRemoteService.getUserAuthentication(userId, UserAuthenticationEnum.ID_AUTH.getCode());
            if(!userAuthentication){
                // 请先实名认证后,在进行转售
                log.info("【OrderInfoServiceImpl.validateMerchantAuditStateAndAuth】 转售失败，用户未实名认证：{}", userId);
                throw OrderException.build(OrderBizErrorCodeEnum.USER_NOT_AUTHENTICATION);
            }
        }
    }

    /**
     * 店铺订单列表
     * @param dto
     * @return
     */
    public Page<PageOrderVO> storeMergePage(PageStoreMergeOrderDTO dto) {
        PageMergeOrderDTO mergeOrderDTO = new PageMergeOrderDTO();
        BeanUtil.copyProperties(dto, mergeOrderDTO);
        PageOrderDTO orderDTO = this.getPageOrderDTO(mergeOrderDTO);
        QueryWrapper<ConsignmentOrderVO> wrapper = new QueryWrapper<>();
        YytUser yytUser = SecurityUtils.getUser();
        Long userId = Objects.requireNonNull(yytUser).getId();
        wrapper.eq("a.del_flag", 0);
        UserInfoVO userInfo = baseRemoteService.getUserInfo(userId);
        if(ObjectUtil.isEmpty(userInfo.getMerchantId()))
            return new Page<>();
        wrapper.eq("b.merchant_id", userInfo.getMerchantId());
        Page page = new Page(dto.getCurrent(), dto.getSize());
        Page<PageOrderVO> pageOrderVOPage = this.getPageOrderVOPage(page, orderDTO, wrapper);
        pageOrderVOPage.getRecords().forEach(order -> {
            order.setMergeStatus(mergeOrderDTO.getMergeStatus());
            // 主图第一张： _ms
            order.setImgUrl(imageUrlService.convertImageUrl(order.getMainImage()).getSmallWebpUrl());
            order.setMainImage(imageUrlService.convertImageUrl(order.getMainImage()).getOriginUrl());

            setRefundLogistics(order,orderLogisticsMapper);
        });
        return pageOrderVOPage;
    }

    /**
     * 获取退款物流
     * @param vo
     * @param orderLogisticsMapper
     */
    private void setRefundLogistics(PageOrderVO vo, OrderLogisticsMapper orderLogisticsMapper) {
        if(StrUtil.isNotBlank(vo.getRefundLogisticsNumber())){
            LambdaQueryWrapper<OrderLogisticsEntity> ew = new LambdaQueryWrapper<OrderLogisticsEntity>()
                    .eq(OrderLogisticsEntity::getOrderId, vo.getRefundId())
                    .eq(OrderLogisticsEntity::getLogisticsNumber, vo.getRefundLogisticsNumber());
            OrderLogisticsEntity orderLogisticsEntity = orderLogisticsMapper.selectOne(ew);
            vo.setRefundLogistics(orderLogisticsEntity);
            boolean upState = logisticsInfoService.validateLogisticsSign(vo.getRefundLogisticsNumber(), String.valueOf(vo.getId()));
            vo.setRefundLogisticsStatus(upState);
        }
    }

    /**
     * 订单入参，状态处理
     * @param mergeOrderDTO
     * @return
     */
    public PageOrderDTO getPageOrderDTO(PageMergeOrderDTO mergeOrderDTO) {
        PageOrderDTO orderDTO = new PageOrderDTO();
        orderDTO.setGoodsName(mergeOrderDTO.getGoodsName());
        orderDTO.setCreateTimeStart(mergeOrderDTO.getCreateTimeStart());
        orderDTO.setCreateTimeEnd(mergeOrderDTO.getCreateTimeEnd());
        orderDTO.setAmountMin(mergeOrderDTO.getAmountMin());
        orderDTO.setAmountMax(mergeOrderDTO.getAmountMax());
        orderDTO.setOrderType(Arrays.asList(OrderEnum.OrderType.FIRST_LAUNCH.getType(),
                OrderEnum.OrderType.CONSIGNMENT.getType(),
                OrderEnum.OrderType.STORE.getType()
        ));
        if(mergeOrderDTO.getMergeStatus() != null){
            orderDTO.setOrderStatus(mergeOrderDTO.getMergeStatus().getOrderStatus());
            if(refund == mergeOrderDTO.getMergeStatus()){
                orderDTO.setProtectStatus(refundStatus);
//                orderDTO.setOrderStatus(null);
            }
        }
        List<PageMergeOrderDTO.MergeStatus> mergeStatusList = mergeOrderDTO.getMergeStatusList();
        if (CollectionUtil.isNotEmpty(mergeStatusList)) {
            List<String> statusList = Lists.newArrayList();
            if (mergeStatusList.contains(refundSaleAfter)) {
                orderDTO.setProtectStatus(refundStatus);
//				mergeStatusList.remove(refundSaleAfter);
//				statusList.addAll(refundSaleAfterStatus);
            }
            statusList.addAll(mergeStatusList.stream().flatMap(item -> item.getOrderStatus().stream()).distinct().toList());
            orderDTO.setOrderStatus(statusList);
        }
        return orderDTO;
    }

    /**
     * 客户端:订单详情-卖家
     * @param idDTO
     * @return
     */
    public OrderDetailVO storeOrderDetail(IdDTO idDTO) {
        QueryWrapper<ConsignmentOrderVO> wrapper = new QueryWrapper<>();
        OrderDetailVO orderDetailVO = this.getOrderDetailVO(Long.valueOf(idDTO.getId()), wrapper);
        if(ObjectUtil.isNotEmpty(orderDetailVO)){
            setRefundLogistics(orderDetailVO,orderLogisticsMapper);
        }
        return orderDetailVO;
    }


    /**
     * 客户端-我的店铺-店铺订单统计
     * @return
     */
    public CountOrderVO countShopOrder() {
        YytUser yytUser = SecurityUtils.getUser();
        Long userId = Objects.requireNonNull(yytUser).getId();
        UserInfoVO userInfo = baseRemoteService.getUserInfo(userId);
        if(ObjectUtil.isEmpty(userInfo.getMerchantId()))
            return new CountOrderVO();
        return this.orderInfoMapper.countShopOrder(userInfo.getMerchantId());

    }

    /**
     * 获取支付分帐对象
     *  自营 资金全部分账到平台， 商户 手续费入平台  其它入商家
     * @param vo
     * @return
     */
    private List<AcctSplitBunchEntity> getAcctInfos(OrderPayParameterVO vo) {
        List<AcctSplitBunchEntity> acctSplitBunches = new ArrayList<>();
        // 平台分账
        AcctSplitBunchEntity platBunch = new AcctSplitBunchEntity();
        // 商家分账
        AcctSplitBunchEntity merchantBunch = new AcctSplitBunchEntity();

        platBunch.setHuifuId(remoteParamService.getByKey("PLATFORM_HUIFU_ACCOUNT").getData());
        // 平台汇付虚拟户id
        if (!MerchantShopTypeEnum.SELF_SUPPORT_SHOP.getType().equals(vo.getShopType())) {
            // 分账接收方ID
            merchantBunch.setHuifuId(vo.getHuifuId());
            // 商户分账金额 = 用户实际支付金额 - 平台实收服务费
            merchantBunch.setAmt(String.valueOf(vo.getAmount().subtract(vo.getFee())));
            acctSplitBunches.add(merchantBunch);
            // 平台分账金额
            platBunch.setAmt(String.valueOf(vo.getFee()));
        } else {
            platBunch.setAmt(String.valueOf(vo.getAmount()));
        }
        acctSplitBunches.add(platBunch);
        return acctSplitBunches;
    }

    /**
     * 根据商户ID查询是否存在未完成的订单
     * 未完成指无法售后的订单
     * @param merchantId
     * @return true-存在、false-不存在
     */
    public boolean hasUnCompleteOrderByMerchantId(Long merchantId){
        return orderInfoMapper.countUnCompleteOrderByMerchantId(merchantId) > 0;
    }

    @Override
    @Transactional
    public void updateOrderComplete(OrderInfoEntity order) {
        if(ObjectUtil.isEmpty(order.getShareCode())) {
            return;
        }
        order.setCompleted(OrderEnum.OrderCompleted.COMPLETED.getStatus());
        updateById(order);
        publisher.publishEvent(new OrderStatusChangeEvent(order.getId(),order.getStatus(),null,OrderEnum.OrderCompleted.COMPLETED.getStatus()));
    }

}
