package com.yts.yyt.order.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.yts.yyt.order.api.enums.AuctionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 竞价申请表
 *
 * <AUTHOR>
 * @date 2025-02-13 11:56:45
 */
@Data
@TableName("auction_apply")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "竞拍申请表")
public class AuctionApplyEntity extends Model<AuctionApplyEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 用户id
	*/
    @Schema(description="用户id")
    private Long userId;

	/**
	* 订单编号
	*/
    @Schema(description="订单编号")
    private String orderNo;

	/**
	* 商品id
	*/
    @Schema(description="商品id")
    private Long goodsId;

	/**
	* 申请品类id
	*/
    @Schema(description="申请品类id")
    private Long goodsTypeId;

	/**
	* 申请数量
	*/
    @Schema(description="申请数量")
    private Integer applyNum;
    /**
     * 快递类型:0.快递上门,1.自行寄出,2.送货上门
     */
    @Schema(description="快递类型:0.快递上门,1.自行寄出,2.送货上门")
    private Integer logisticsType;

	/**
	* 申请图片
	*/
    @Schema(description="申请图片")
    private String applyImg;

	/**
	* 申请名称
	*/
    @Schema(description="申请名称")
    private String applyName;

	/**
	* 收货编码
	*/
    @Schema(description="收货编码")
    private String receiptCode;


	/**
	* @see AuctionEnum.ApplyStatus
	*/
    @Schema(description="竞拍状态")
    private String auctionStatus;

	/**
	* @see AuctionEnum.ApplySubStatusEnum
	*/
    @Schema(description="竞拍子状态")
    private String auctionSubStatus;

	/**
	* 确认过期时间
	*/
    @Schema(description="确认过期时间")
    private LocalDateTime expirseTime;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	@Schema(description="可重新拍卖时间")
    private LocalDateTime reAuctionTime;

	@Schema(description = "竞拍开始时间")
	private LocalDateTime auctionBeginTime;

	@Schema(description = "竞拍结束时间")
	private LocalDateTime auctionEndTime;

	@Schema(description = "竞拍最小估价")
	private BigDecimal minAuctionPrice;

	@Schema(description = "竞拍最大估价")
	private BigDecimal maxAuctionPrice;

	@Schema(description="删除状态 0-未删除 1-已删除")
	private Integer deleted;

	@Schema(description="产地")
	private String placeOfOrigin;

	@Schema(description = "竞拍成交价")
	private BigDecimal soldPrice;

	@Schema(description = "竞拍成交手续费")
	private BigDecimal soldFee;
	
	@Schema(description = "竞拍备注")
	private String remark;

	@Schema(description = "寄售时间")
	private LocalDateTime consignmentTime;

	@Schema(description = "取消寄售时间")
	private LocalDateTime cancelledConsignmentTime;

	@Schema(description = "竞拍完成时间")
	private LocalDateTime completionTime;

	@Schema(description = "付款时间")
	private LocalDateTime paymentTime;
	
	
	
	
	

}
