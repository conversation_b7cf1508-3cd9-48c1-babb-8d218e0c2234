package com.yts.yyt.order.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;


/**
 * 寄售商口DTO
 */
@Data
@Schema(description = "首发订单请求参数")
public class FirstLaunchOrderDTO implements Serializable {

	@Schema(description="主键")
    @NotNull
	private Long orderId;

	/**
	 * 用户ID
	 */
	@Schema(description="用户ID")
	private Long userId;

	/**
	 * 运单号
	 */
	@Schema(description="运单号")
    @NotBlank
	private String logisticsNumber;

    /**
     * 快递公司
     */
    @Schema(description="快递公司")
    private String logisticsCompany;

}
