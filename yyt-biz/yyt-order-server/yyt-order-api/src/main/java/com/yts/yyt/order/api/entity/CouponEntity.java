package com.yts.yyt.order.api.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yts.yyt.order.api.enums.CouponEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 优惠券模板实体
 *
 * <AUTHOR>
 * @date 2024-02-13
 */
@Data
@TableName("coupon")
@Schema(description = "优惠券模板实体")
public class CouponEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动ID")
    private Long couponActivityId;

    @Schema(description = "优惠券名称（如：新用户10元券）")
    private String name;

    @Schema(description = "优惠固定金额（如：10.00）")
    private BigDecimal amount;

    /**
     * 使用平台，多个平台用逗号分隔，如：1,2,3
     * 
     * @see CouponEnum.Platform
     */
    @Schema(description = "使用平台：1-APP 2-小程序 3-全平台，多个平台用逗号分隔")
    private String platform;

    @Schema(description = "领取后有效天数（如：30）")
    private Integer validityDays;

    @Schema(description = "满减门槛（如：100.00表示满100可用）")
    private BigDecimal limitAmount;

    /**
     * 店铺范围
     * 
     * @see CouponEnum.ShopScope
     */
    @Schema(description = "店铺范围")
    private String shopScope;

    @Schema(description = "等级范围")
    private Integer levelScope;

    /**
     * 状态
     * 
     * @see CouponEnum.Status
     */
    @Schema(description = "状态：1-启用 0-禁用")
    private Integer status;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "是否删除: 0=否, 1=是")
    private String delFlag;
}
