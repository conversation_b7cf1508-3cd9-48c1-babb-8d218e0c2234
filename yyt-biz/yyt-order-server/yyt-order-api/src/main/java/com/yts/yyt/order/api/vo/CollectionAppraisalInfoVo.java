package com.yts.yyt.order.api.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 藏品鉴定信息表
 *
 * <AUTHOR>
 * @date 2025-02-14 17:57:47
 */
@Data
@Schema(description = "藏品鉴定信息Vo")
public class CollectionAppraisalInfoVo implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.ASSIGN_ID)
	@Schema(description="主键")
	private Long id;

	/**
	 * 鉴定单号
	 */
	@Schema(description="鉴定单号")
	private String collectionNo;


	/**
	 * 藏品名称
	 */
	@Schema(description="藏品名称")
	private String collectionName;

	/**
	 * 藏品产地
	 */
	@Schema(description="藏品产地")
	private String collectionOrigin;

	/**
	 * 藏品图片
	 */
	@Schema(description="藏品图片")
	private String collectionImage;

	/**
	 * 藏品视频
	 */
	@Schema(description="藏品视频")
	private String collectionVideo;

	/**
	 * 购买原价
	 */
	@Schema(description="购买原价")
	private BigDecimal purchasePrice;

	/**
	 * 尺寸大小（长）
	 */
	@Schema(description="尺寸大小（长）")
	private BigDecimal sizeLength;

	/**
	 * 尺寸大小（宽）
	 */
	@Schema(description="尺寸大小（宽）")
	private BigDecimal sizeWidth;

	/**
	 * 尺寸大小（高）
	 */
	@Schema(description="尺寸大小（高）")
	private BigDecimal sizeHeight;

	/**
	 * 藏品直径
	 */
	@Schema(description="藏品直径")
	private BigDecimal collectionDiameter;

	/**
	 * 补充说明
	 */
	@Schema(description="补充说明")
	private String additionalNotes;

	/**
	 * 鉴定状态：0-取消鉴定、1-待估价、2-已估价、3-鉴定失败、4-鉴定中
	 * AppraisalStateEnum
	 */
	@Schema(description="鉴定状态：0-取消鉴定、1-待估价、2-已估价、3-鉴定失败、4-鉴定中")
	private Integer appraisalState;

	/**
	 * 鉴定估价最高
	 */
	@Schema(description="鉴定估价最高")
	private BigDecimal appraisalValueMax;

	/**
	 * 鉴定估价最低
	 */
	@Schema(description="鉴定估价最低")
	private BigDecimal appraisalValueMin;

	/**
	 * 归属者
	 */
	@Schema(description="归属者")
	private String owner;

	/**
	 * 归属者姓名
	 */
	@Schema(description="归属者姓名")
	private String ownerName;


	/**
	 * 评估结果
	 */
	@Schema(description="评估结果")
	private String appraisalResult;

	/**
	 * 想要人次
	 */
	@Schema(description="想要人次")
	private Integer wantCount;


	/**
	 * 商品年代ID
	 */
	@Schema(description = "商品年代ID")
	private Long goodsEraId;

	/**
	 * 商品品相ID
	 */
	@Schema(description = "商品品相ID")
	private Long goodsConditionId;

	/**
	 * 商品一级品类ID
	 */
	@Schema(description = "商品一级品类ID")
	private Long goodsTypeId;

	/**
	 * 商品二级品类ID
	 */
	@Schema(description = "商品二级品类ID")
	private Long goodsCategoryId;

	/**
	 * 商品器型ID
	 */
	@Schema(description = "商品器型ID")
	private Long goodsShapeId;

	/**
	 * 商品颜色ID
	 */
	@Schema(description = "商品颜色ID")
	private Long goodsColorId;

	/**
	 * 商品纹路ID
	 */
	@Schema(description = "商品纹路ID")
	private Long goodsPatternId;

	/**
	 * 商品材料
	 */
	@Schema(description = "商品材料")
	private String material;


	/**
	 * 商品标签id
	 */
	@Schema(description = "商品标签id")
	private Long goodsLableId;


	/**
	 * 想要标识 1-是、0-否
	 */
	@Schema(description="想要标识")
	private Integer wantFlag;

	/**
	 * 头像
	 */
	@Schema(description="头像")
	private String headImg;

	/**
	 * 手机号
	 */
	@Schema(description="手机号")
	private String mobile;

	/**
	 * 用户昵称
	 */
	@Schema(description="用户昵称")
	private String nickname;


	/**
	 * 创建人id
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建人id")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField(fill = FieldFill.INSERT)
	@Schema(description="创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新人id
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="更新人id")
	private String updateBy;

	/**
	 * 修改时间
	 */
	@TableField(fill = FieldFill.INSERT_UPDATE)
	@Schema(description="修改时间")
	private LocalDateTime updateTime;
}
