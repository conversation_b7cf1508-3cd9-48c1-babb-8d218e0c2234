package com.yts.yyt.merchant.api.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Schema(description = "佣金结算数据统计vo")
@Accessors(chain = true)
public class IncentiveCommissionNumVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description="总数")
    private int totalNum;

    @Schema(description="未上传发票数")
    private int unUpdateFileNum;

    @Schema(description="已上传发票数")
    private int updateFileNum;

    @Schema(description="已发生数")
    private int sendFileNum;
}
