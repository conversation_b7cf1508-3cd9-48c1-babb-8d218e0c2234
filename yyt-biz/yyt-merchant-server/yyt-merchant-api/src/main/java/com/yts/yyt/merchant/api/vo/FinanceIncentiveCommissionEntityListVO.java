package com.yts.yyt.merchant.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "财务佣金结算表")
public class FinanceIncentiveCommissionEntityListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 数据ID
     */
    @Schema(description="数据ID")
    private Long id;


//    /**
//     * 周期结束时间
//     */
//    @Schema(description="周期结束时间")
//    private LocalDateTime cycleEnd;
//
//    /**
//     * 周期开始时间
//     */
//    @Schema(description="周期开始时间")
//    private LocalDateTime cycleStart;


        /**
     * 统计周期
     */
    @Schema(description="统计周期")
    private String reportCycle;



    /**
     * 统计时间
     */
    @Schema(description="统计时间")
    private String reportDate;

    /**
     * 合伙人名称
     */
    @Schema(description="合伙人名称")
    private String partnerName;


    /**
     * 角色对应用户ID
     */
    @Schema(description="角色对应用户ID")
    private Long roleUid;

    /**
     * 基础佣金（元）
     */
    @Schema(description="基础佣金（元）")
    private BigDecimal commissionBase;

    /**
     * 阶梯冲刺奖励（元
     */
    @Schema(description="阶梯冲刺奖励（元）")
    private BigDecimal commissionLadder;


    /**
     * 阶梯冲刺奖励（元
     */
    @Schema(description="专项激励（元）")
    private BigDecimal commissionSpecial;

    /**
     * 销售佣金（元）
     */
    @Schema(description="销售佣金（元）")
    private BigDecimal commissionSale;

    /**
     * 总金额（元）
     */
    @Schema(description="总金额（元）")
    private BigDecimal commissionTotal;


    /**
     * 状态：见字典incentive_commission_status
     * @see com.yts.yyt.merchant.api.enums.IncentiveCommissionStatusEnum
     */
    @Schema(description="状态：见字典incentive_commission_status")
    private String status;


    /**
     * 发放时间
     */
    @Schema(description="发放时间")
    private LocalDateTime grantTime;


    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    /**
     * 交易流水号
     */
    @Schema(description="交易流水号")
    private String tradeSerialNo;




    /**
     * 银行名称
     */
    @Schema(description="银行名称")
    private String bankName;

    /**
     * 银行账户
     */
    @Schema(description="银行账户")
    private String bankAccount;

    /**
     * 银行账户
     */
    @Schema(description="银行卡号")
    private String bankNo;

    /**
     * 凭证图片列表
     */
    @Schema(description="(已发放)凭证图片列表")
    private String beenVoucherImgs;


    /**
     * 备注
     */
    @Schema(description="备注")
    private String remarks;


    /**
     * 发票
     */
    @Schema(description="发票地址")
    private String invoiceImg;

    /**
     * 发票关联号
     */
    @Schema(description="发票关联号")
    private String invoiceNo;

    /**
     * 开始时间
     */
    @Schema(description="开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description="结束时间")
    private LocalDateTime endTime;

    /**
     * 角色类型：见字典commossion_role_type(1=商家 2=合伙人)
     */
    @Schema(description="角色类型：见字典commossion_role_type(1=商家 2=合伙人)")
    private String roleType;

    /**
     *合伙人系统用户ID
     */
    @Schema(description="合伙人系统用户ID")
    private Long sysUserId;

    /**
     * (未发放)商户银行卡数据ID
     */
    @Schema(description="(未发放)商户银行卡数据ID")
    private String unMerchantBankId;
}

