package com.yts.yyt.merchant.api.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serial;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("incentive_commission_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "佣金发放记录表")
public class IncentiveCommissionLog  extends Model<IncentiveCommissionLog>  {
    @Serial
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Schema(description="主键ID")
    private Long id;
    
    /**
     * 账单金额（元）
     */
    @Schema(description="账单金额（元）")
    private BigDecimal amount;
    
    /**
     * 银行账户
     */
    @Schema(description="银行账户")
    private String bankAccount;
    
    /**
     * 银行名称
     */
    @Schema(description="银行名称")
    private String bankName;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;
    
    /**
     * 创建人id
     */
    @Schema(description="创建人id")
    private Long createById;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;
    
    /**
     * 删除状态 0否 1是
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除状态 0否 1是")
    private Integer delFlag;
    
    /**
     * 激励佣金账单ID
     */
    @Schema(description="激励佣金账单ID")
    private Long incentiveCommissionId;
    
    /**
     * 收款单位/人
     */
    @Schema(description="收款单位/人")
    private String payee;
    
    /**
     * 收款单位/人电话
     */
    @Schema(description="收款单位/人电话")
    private String payeeMobile;
    
    /**
     * 实际发放金额（元）
     */
    @Schema(description="实际发放金额（元）")
    private BigDecimal realAmount;
    
    /**
     * 备注信息
     */
    @Schema(description="备注信息")
    private String remarks;
    
    /**
     * 租户id
     */
    @Schema(description="租户id")
    private Long tenantId;
    
    /**
     * 交易流水号
     */
    @Schema(description="交易流水号")
    private String tradeSerialNo;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;
    
    /**
     * 更新人id
     */
    @Schema(description="更新人id")
    private Long updateById;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;
    
    /**
     * 凭证图片列表
     */
    @Schema(description="凭证图片列表")
    private String voucherImgs;
    

}

