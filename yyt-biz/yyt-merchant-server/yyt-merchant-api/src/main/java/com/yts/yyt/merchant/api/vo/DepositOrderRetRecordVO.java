package com.yts.yyt.merchant.api.vo;

import com.yts.yyt.merchant.api.enums.DepositEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "消保金退款记录")
public class DepositOrderRetRecordVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "退款金额")
    private BigDecimal retAmount;

    @Schema(description = "退款状态" , implementation = DepositEnum.RetStatusEnum.class)
    private Integer retStatus;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "退款订单数")
    private Integer retCount;

    private List<RetRecord> retRecords;

    @Data
    @Accessors(chain = true)
    public static class RetRecord{
        @Schema(description = "退款金额")
        private BigDecimal amount;

        @Schema(description = "退款渠道")
        private String retChannel;

        @Schema(description = "退款时间")
        private LocalDateTime retTime;

        @Schema(description = "退款状态" , implementation = DepositEnum.RetStatusEnum.class)
        private Integer retStatus;
    }
}
