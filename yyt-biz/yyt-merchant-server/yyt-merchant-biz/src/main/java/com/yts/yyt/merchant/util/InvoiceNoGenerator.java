package com.yts.yyt.merchant.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Component
@RequiredArgsConstructor
public class InvoiceNoGenerator {

    private final RedisTemplate<String, Object> redisTemplate;

    private static final String INVOICE_NO_KEY = "invoice:no:%s";

    public String getInvoiceNo() {

        String date = LocalDateTimeUtil.format(LocalDate.now(), DateTimeFormatter.BASIC_ISO_DATE);
        String key = String.format(INVOICE_NO_KEY, date);
        BoundValueOperations<String, Object> valueOperations = redisTemplate.boundValueOps(key);
        Long no = valueOperations.increment();
        if (no == 1) {
            valueOperations.expire(1, TimeUnit.DAYS);
        }

        return String.format("%s%03d", date, no);
    }
}
