package com.yts.yyt.merchant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.common.alert.annotation.AlertException;
import com.yts.yyt.common.alert.enums.AlertLevel;
import com.yts.yyt.common.alert.model.AlertMessage;
import com.yts.yyt.common.alert.notify.AlertNotifier;
import com.yts.yyt.common.core.constant.TransactionSettingCodeConstants;
import com.yts.yyt.common.core.exception.ParamsValidateException;
import com.yts.yyt.common.core.util.PageHelper;
import com.yts.yyt.common.data.resolver.ParamResolver;
import com.yts.yyt.common.pay.huifu.dto.HuifuRefundDTO;
import com.yts.yyt.common.pay.huifu.dto.TradeAcctpaymentPayDTO;
import com.yts.yyt.common.pay.huifu.service.HuiFuTradeAcctpaymentService;
import com.yts.yyt.common.pay.huifu.vo.ScanPayRefundVO;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.rocketmq.template.DepositTransferRocketMQTemplate;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.goods.api.vo.ShopGoodsLotCountVO;
import com.yts.yyt.merchant.api.constant.Constant;
import com.yts.yyt.merchant.api.dto.*;
import com.yts.yyt.merchant.api.entity.DepositRetRecordEntity;
import com.yts.yyt.merchant.api.entity.MerchantDepositOrderEntity;
import com.yts.yyt.merchant.api.entity.MerchantEntity;
import com.yts.yyt.merchant.api.enums.DepositEnum;
import com.yts.yyt.merchant.api.enums.MerchantShopTypeEnum;
import com.yts.yyt.merchant.api.exception.MerchantErrorEnum;
import com.yts.yyt.merchant.api.exception.MerchantException;
import com.yts.yyt.merchant.api.vo.*;
import com.yts.yyt.merchant.mapper.MerchantDepositOrderMapper;
import com.yts.yyt.merchant.mq.dto.DepositOrderTransferMqDTO;
import com.yts.yyt.merchant.service.*;
import com.yts.yyt.order.api.dto.WxOrderShippingDTO;
import com.yts.yyt.order.api.enums.OrderParamEnum;
import com.yts.yyt.user.api.constant.TradeDescConstants;
import com.yts.yyt.user.api.constant.enums.AccountEnum;
import com.yts.yyt.user.api.dto.AccountOperateDTO;
import com.yts.yyt.user.api.dto.AccountTransferDTO;
import com.yts.yyt.user.api.vo.AccountVO;
import com.yts.yyt.user.api.vo.UserInfoVO;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service("merchantDepositOrderService")
public class MerchantDepositOrderServiceImpl extends ServiceImpl<MerchantDepositOrderMapper, MerchantDepositOrderEntity>
 implements MerchantDepositOrderService {

    private final DepositConfigService depositConfigService;

    private final MerchantService merchantService;

    private final DepositRetRecordService depositRetRecordService;

    private final BaseRemoteService baseRemoteService;

    private final AlertNotifier alertNotifier;

    private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;

    @Value("${spring.profiles.active:dev}")
    private String env;

    @Override
    public MerchantDepositOrderPayParamVO getPayParam(MerchantDepositPayDTO dto) {
        log.info("[获取消保金订单参数],请求参数: {}",dto);

        // 通过用户ID获取商户信息
        MerchantEntity merchant = merchantService.getMerchantInfoByUserId(dto.getUserId());

        // 获取消保金配置
        DepositConfigVO depositConfig = depositConfigService.confInfoByKey(dto.getDepositKey());

        //判断商户店铺类型与消保金权益组别是否一致
        String shopEquityType = DepositEnum.ConfEquityGroup.getConfEquityGroupByShopType(merchant.getShopType());
        if(!Objects.equals(shopEquityType, depositConfig.getConfGroup())){
            throw MerchantException.build(MerchantErrorEnum.MERCHANT_DEPOSIT_GROUP_ERROR);
        }
        // 获取用户消保金余额并计算应缴纳金额
        AccountVO accountVO = baseRemoteService.getAccountInfo(dto.getUserId(), AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        BigDecimal userRepayAmount = new BigDecimal(depositConfig.getConfValue()).subtract(accountVO.getAvailableBalance());
        if(userRepayAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw MerchantException.build(MerchantErrorEnum.DEPOSIT_REPAY_AMOUNT_ERROR);
        }

        // 生成支付订单
        MerchantDepositOrderEntity entity = new MerchantDepositOrderEntity();
        entity.setAmount(userRepayAmount)
                .setFee(new BigDecimal(0.00))
                .setPayChannel(dto.getPayChannel())
                .setType(DepositEnum.OrderType.PAY.getType())
                .setUserId(dto.getUserId())
                .setMerchantId(merchant.getId())
                .setStatus(DepositEnum.OrderStatus.PENDING.getStatus());
        save(entity);

        // 手续费支付模式
        String feeFlag = baseRemoteService.getParamValByKey(OrderParamEnum.FEE_FLAG.getKey());
        MerchantDepositOrderPayParamVO vo = new MerchantDepositOrderPayParamVO();
        vo.setAmount(userRepayAmount)
                .setFee(entity.getFee())
                .setUserId(entity.getUserId())
                .setOrderId(entity.getId())
                .setGoodsName(Constant.DEPOSIT_GOOD_NAME)
                .setGoodsNo(String.format(Constant.DEPOSIT_GOOD_NO, DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss"), RandomUtil.randomInt(999999)))
                .setExpirseTime(entity.getCreateTime().plusHours(4))
                .setFeeFlag(feeFlag)
                .setDelayAcctFlag("N")
                .setAcctSplitBunchFlag("N")
                .setOrderType(MerchantDepositOrderPayParamVO.PAYBACK)
                .setAcctId(ParamResolver.getStr("PLATFORM_HUIFU_DEPOSIT_ACCOUNT"));

        if(DepositEnum.PayChannelTypeEnum.isWeixinPay(dto.getPayChannel())) {
            UserInfoVO userInfo = baseRemoteService.getUserInfo(vo.getUserId());
            vo.setWxSubOpenid(userInfo.getWeappOpenid());
        }
        log.info("[获取消保金订单参数],响应: {}",vo);
        return vo;
    }


    @Override
    public UserDepositRepayAmountVO getRepayAmount(String depositKey) {
        UserDepositRepayAmountVO vo = new UserDepositRepayAmountVO();
        vo.setUserId(SecurityUtils.getUser().getId());

        //获取用户消保金余额
        AccountVO accountVO = baseRemoteService.getAccountInfo(vo.getUserId(), AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        vo.setAmount(accountVO.getTotalBalance());

        //计算补缴金额
        //获取消保金配置
        DepositConfigVO depositConfig = depositConfigService.confInfoByKey(depositKey);
        //计算补缴金额
        BigDecimal repayAmount = new BigDecimal(depositConfig.getConfValue()).subtract(vo.getAmount());

        vo.setRepayAmount( repayAmount.compareTo(BigDecimal.ZERO) > 0 ? repayAmount : BigDecimal.ZERO);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#dto.orderId"})
    public Boolean payDepositCallBack(@Valid MerchantDepositOrderPayCallbackDTO dto) {
        log.info("[消保金订单支付回调],参数:{}", JSON.toJSONString(dto));
        //校验同一个支付流水号只能生成一个订单
        MerchantDepositOrderEntity entity = getById(dto.getOrderId());
        if(entity == null){
            throw MerchantException.build(MerchantErrorEnum.DEPOSIT_ORDER_NOT_EXIST);
        }
        if(!ObjectUtil.equals(entity.getStatus(), DepositEnum.OrderStatus.PENDING.getStatus())) {
            throw MerchantException.build(MerchantErrorEnum.DEPOSIT_ORDER_HAS_PAY);
        }
        if(DepositEnum.PayStatus.SUCCESS.getType() == dto.getPayStatus()) {
            entity.setPaySerio(dto.getPaySerio());
            entity.setTransactionId(dto.getOutTransId());
            entity.setStatus(DepositEnum.OrderStatus.BEEN_PAY.getStatus());
            entity.setPayTime(dto.getTradeTime());
            // 增加用户资金余额
            AccountOperateDTO accountOperateDTO = new AccountOperateDTO();
            accountOperateDTO.setUserId(entity.getUserId());
            accountOperateDTO.setAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
            accountOperateDTO.setAmount(entity.getAmount());
            accountOperateDTO.setBusinessId(entity.getId().toString());
            accountOperateDTO.setTradeDesc(TradeDescConstants.DEPOSIT_PAYMENT_SUCCESS);
            accountOperateDTO.setBusinessTradeDesc(TradeDescConstants.DEPOSIT_PAY);
            accountOperateDTO.setBusinessType(AccountEnum.BusinessTypeEnum.DEPOSIT);
            AccountVO vo = baseRemoteService.addBalance(accountOperateDTO);
            // 处理商户消保金权益
            recalculateMerchantLevel(entity,vo);
            // 小程序支付自动触发发货
            if(DepositEnum.PayChannelTypeEnum.T_MINIAPP.getType().equals(entity.getPayChannel())) {
                // 执行小程序订单发货
                wxMaOrderShipping(entity,dto.getWeAppOpenid());
            }
        }else {
            entity.setStatus(DepositEnum.OrderStatus.FAIL.getStatus());
        }
        return updateById(entity);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @AlertException(value = "发起转出消保金异常",modules = "商户",level = AlertLevel.CRITICAL)
    @Lock4j(keys = {"#userId"}, expire = 3000, acquireTimeout = 2000)
    public Boolean transferDeposit(BigDecimal amount, Long userId){
        log.info("[消保金转帐] ---------->>> 参数, amount: {}, userId: {}", amount, userId);
        // 1. 创建订单并进行预处理
        Long orderId = preTransferDeposit(amount, userId);

        // 2. 发送事务消息进行支付处理
        try {
            sendTransferMessage(orderId, amount, userId);
        } catch (ClientException e) {
            log.error("[消保金转帐] MQ发送异常", e);
            throw MerchantException.build(MerchantErrorEnum.MQ_SEND_ERROR);
        }
        log.info("[消保金转帐] ---------->>> 结束执行, amount: {}, userId: {}", amount, userId);
        return true;
    }

    @AlertException(value = "消保金转出-发起支付异常",modules = "商户",level = AlertLevel.CRITICAL)
    @Transactional
    public Boolean tradeAcctpayment(DepositOrderTransferMqDTO dto){
        log.info("[消保金转帐] ---------->>> 调用三方支付(退回)消保金 , dto: {}", dto);
        MerchantDepositOrderEntity entity = this.getById(dto.getOrderId());
        if(entity == null || !ObjectUtil.equals(entity.getStatus(), DepositEnum.OrderStatus.PROCESSING.getStatus())) {
            log.error("[消保金转帐] ---------->>> 支付消保金订单不存在或状态异常, dto: {}", dto);
            throw new ParamsValidateException(MerchantErrorEnum.PARAMS_ERROR.getMsg());
        }
        // 原路退回
        Boolean flag = executeRetDepositOrderPayment(entity);
        log.info("[消保金转帐] ---------->>> 调用三方支付消保金成功, dto: {}", dto);
        return flag;
    }

    @AlertException(value = "消保金退回-支付回调异常",modules = "商户",level = AlertLevel.CRITICAL)
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @Lock4j(keys = {"#dto.orderId"})
    @Override
    public Boolean depositRefundCallBack(MerchantDepositOrderRefundCallbackDTO dto){
        log.info("[消保金转帐] ---------->>> 支付回调, dto: {}", dto);
        MerchantDepositOrderEntity entity = getById(dto.getOrderId());
        entity.setRetStatus(DepositEnum.RetStatusEnum.RET_STATUS_RETURNED.getRetStatus());
        updateById(entity);
        DepositRetRecordEntity retRecord = this.depositRetRecordService.findByRetId(entity.getId());
        retRecord.setRetTime(dto.getTradeTime());
        retRecord.setRetStatus(DepositEnum.RetStatusEnum.RET_STATUS_RETURNED.getRetStatus());
        depositRetRecordService.updateById(retRecord);
        List<MerchantDepositOrderEntity> list = getPayDepositOrderByUserIdAndRetStatus(entity.getUserId(),DepositEnum.RetStatusEnum.RET_STATUS_PROCESSING.getRetStatus());
        if(CollectionUtil.isEmpty(list)) {
            log.info("[消保金转帐] ---------->>> 全部退回完成, userId: {}", entity.getUserId());
        }
        return true;
    }

    @AlertException(value = "操作消保金账户异常",modules = "商户",level = AlertLevel.CRITICAL)
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#orderId"})
    public void doTransferDepositBusiness(Long orderId,BigDecimal amount){
        log.info("[转出消保金] ---------->>> 执行业务逻辑, orderId: {}, amount: {}", orderId, amount);
        // 操作资金账户
        MerchantDepositOrderEntity entity = getById(orderId);
        if(entity == null || !ObjectUtil.equals(entity.getStatus(), DepositEnum.OrderStatus.PROCESSING.getStatus())){
            log.error("[转出消保金] ---------->>> 执行业务逻辑, orderId: {}", orderId);
            throw new ParamsValidateException(MerchantErrorEnum.PARAMS_ERROR.getMsg());
        }
        // 减少用户可用余额
        baseRemoteService.reduceBalance(getAccountOperateDTO(entity));
        // 执行业务修改
        MerchantEntity merchant = merchantService.getMerchantInfoByUserId(entity.getUserId());
        merchant.setBenefitLevel("") ;
        merchantService.updateById(merchant);

        entity.setStatus(DepositEnum.OrderStatus.BEEN_PAY.getStatus());
        entity.setPayTime(LocalDateTime.now());
        updateById(entity);
        log.info("[转出消保金] ---------->>> 执行业务逻辑完成, orderId: {}, amount: {}", orderId, amount);
    }

    private AccountOperateDTO getAccountOperateDTO(MerchantDepositOrderEntity entity){
        AccountOperateDTO dto = new AccountOperateDTO();
        dto.setUserId(entity.getUserId());
        dto.setAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        dto.setAmount(entity.getAmount());
        dto.setBusinessId(String.valueOf(entity.getId()));
        dto.setTradeDesc(TradeDescConstants.DEPOSIT_RETURN);
        dto.setBusinessTradeDesc(TradeDescConstants.DEPOSIT_RETURN);
        dto.setBusinessType(AccountEnum.BusinessTypeEnum.DEPOSIT);
        return dto;
    }

    @NotNull
    private AccountTransferDTO getAccountTransferDTO(BigDecimal amount, MerchantDepositOrderEntity entity) {
        AccountTransferDTO dto = new AccountTransferDTO();
        dto.setAmount(amount);
        dto.setUserId(entity.getUserId());
        dto.setFromAccountType(AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        dto.setToAccountType(AccountEnum.AccountTypeEnum.BALANCE_ACCOUNT.getType());
        dto.setFromBusinessTradeDesc(TradeDescConstants.DEPOSIT_RETURN);
        dto.setToBusinessTradeDesc(TradeDescConstants.DEPOSIT_TRANSFER);
        dto.setBusinessId(String.valueOf(entity.getId()));
        return dto;
    }

    /**
     * 小程序订单发货
     * @param entity
     */
    private void wxMaOrderShipping(MerchantDepositOrderEntity entity,String openId) {
        WxOrderShippingDTO dto = new WxOrderShippingDTO();
        dto.setOrderNumberType(2);
        dto.setTransactionId(entity.getTransactionId());
        dto.setLogisticsType(3);
        dto.setDeliveryMode(1);
        dto.setItemDesc("平台商家保证金");
        dto.setBusinessId(entity.getId());
        dto.setOpenid(openId);
        try {
            baseRemoteService.saveWxOrderShipping(dto);
        } catch (Exception e) {
            // 发货异常不用处理，有补偿机制
            log.error("小程序订单发货，参数:{}" , JSON.toJSONString(entity));
            log.error(e.getMessage(),e);
        }
    }

    @Override
    public Page<MerchantDepositOrderVO> page(MerchantDepositOrderQueryDTO dto) {
        LambdaQueryWrapper<MerchantDepositOrderEntity> query = Wrappers.lambdaQuery();
        if(ObjectUtil.isNotEmpty(dto.getStatus())) {
            query.eq(MerchantDepositOrderEntity::getStatus,dto.getStatus());
        }
        if(ObjectUtil.isNotEmpty(dto.getMerchantId())) {
            query.eq(MerchantDepositOrderEntity::getMerchantId,dto.getMerchantId());
        }
        if(ObjectUtil.isNotEmpty(dto.getUserId())){
            query.eq(MerchantDepositOrderEntity::getUserId,dto.getUserId());
        }
        if(ObjectUtil.isNotEmpty(dto.getType())){
            query.eq(MerchantDepositOrderEntity::getType,dto.getType());
        }
        query.orderByDesc(MerchantDepositOrderEntity::getCreateTime);

        Page<MerchantDepositOrderEntity> page = this.page(new Page<>(dto.getCurrent(), dto.getSize()),query);
        Page<MerchantDepositOrderVO> resPage = PageHelper.toPage(page,MerchantDepositOrderVO.class,(f,t)->{
            t.setOrderTypeDesc(DepositEnum.OrderType.getDescByType(f.getType()));
        });

        return resPage;
    }

    /**
     *  重新计算商户的权益等级
     * @param depositOrder 消保金订单信息
     * @param vo 账户信息
     */
    private void recalculateMerchantLevel(MerchantDepositOrderEntity depositOrder, AccountVO vo) {
        // 获取通过用户ID获取商户ID
        MerchantEntity merchant = merchantService.getById(depositOrder.getMerchantId());
        List<DepositConfigVO> configs;
        if(ObjectUtil.equals(MerchantShopTypeEnum.MERCHANT_SHOP.getType(),  merchant.getShopType())) {
            configs = depositConfigService.confList(DepositEnum.ConfEquityGroup.MERCHANT_GROUP.getGroup());
        }else {
            configs = depositConfigService.confList(DepositEnum.ConfEquityGroup.PERSON_GROUP.getGroup());
        }
        log.info("计算商户的权益等级,depositOrder:{},account:{},configs:{}",depositOrder,vo,configs);
        // 根据当前消保金余额匹配对应的权益等级
        String equityLevel = null;
        BigDecimal userBalance = vo.getTotalBalance();

        List<DepositConfigVO> sortedConfigs = new ArrayList<>(configs);
        sortedConfigs.sort((a, b) -> new BigDecimal(b.getConfValue()).compareTo(new BigDecimal(a.getConfValue())));

        for (DepositConfigVO config : sortedConfigs) {
            BigDecimal configAmount = new BigDecimal(config.getConfValue());
            if (userBalance.compareTo(configAmount) >= 0) {
                equityLevel = config.getConfKey();
                break;
            }
        }
        // 更新商户权益等级
        if (StrUtil.isNotBlank(equityLevel)) {
            merchant.setBenefitLevel(equityLevel);
            merchantService.updateById(merchant);
        }
    }

    private Long preTransferDeposit(BigDecimal amount, Long userId) {
        // 1. 获取商家信息
        MerchantEntity merchant = merchantService.getMerchantInfoByUserId(userId);
        if (ObjectUtil.isNull(merchant)) {
            log.error("[转出消保金] 商家不存在, userId: {}", userId);
            throw new ParamsValidateException(MerchantErrorEnum.MERCHANT_NOT_EXIST.getMsg());
        }

        // 2. 判断是否有在售的拍口
        ShopGoodsLotCountVO goodsLotCountVO = baseRemoteService.getLotCountByMerchantId(merchant.getId());
        if (goodsLotCountVO.getSellingCount() > 0) {
            log.info("[转出消保金] 存在在售拍品, merchantId: {} , goodsLotCountVO:{}", merchant.getId(), goodsLotCountVO);
            throw new ParamsValidateException(MerchantErrorEnum.HAS_ON_SALE_LOT.getMsg());
        }

        // 3. 判断是否存在未完成的订单
        boolean hasUnfinishedOrder = baseRemoteService.hasUnCompleteOrderByMerchantId(merchant.getId());
        if (hasUnfinishedOrder) {
            log.info("[转出消保金] 存在未完成订单, merchantId: {}", merchant.getId());
            throw new ParamsValidateException(MerchantErrorEnum.HAS_UNFINISHED_ORDER.getMsg());
        }

        // 4.判断是否存在处理中的订单
        if(findProcessingOrderByUserId(userId) != null){
            log.info("[转出消保金] 存在处理中的订单, userId: {}", userId);
            throw new ParamsValidateException(MerchantErrorEnum.HAS_PROCESSING_ORDER.getMsg());
        }

        // 5.校验余额
        List<MerchantDepositOrderEntity> depositOrders = getPayDepositOrderByUserIdAndRetStatus(userId,DepositEnum.RetStatusEnum.RET_STATUS_NOT_RETURNED.getRetStatus());
        if(CollectionUtil.isEmpty(depositOrders)){
            throw new ParamsValidateException(MerchantErrorEnum.DEPOSIT_BALANCE_NOT_ENOUGH.getMsg());
        }
        BigDecimal transferAmt = depositOrders.stream().map(MerchantDepositOrderEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        AccountVO accountVO = baseRemoteService.getAccountInfo(userId, AccountEnum.AccountTypeEnum.DEPOSIT_ACCOUNT.getType());
        if (transferAmt.compareTo(amount) <0 || accountVO.getAvailableBalance().compareTo(amount) < 0) {
            throw new ParamsValidateException(MerchantErrorEnum.DEPOSIT_BALANCE_NOT_ENOUGH.getMsg());
        }

        // 6. 生成退回订单（初始状态为处理中）
        MerchantDepositOrderEntity entity = new MerchantDepositOrderEntity();
        entity.setAmount(amount)
                .setFee(new BigDecimal(0.00))
                .setType(DepositEnum.OrderType.REFUND.getType())
                .setUserId(userId)
                .setMerchantId(merchant.getId())
                .setStatus(DepositEnum.OrderStatus.PROCESSING.getStatus())  // 初始状态为处理中
                .setCreateTime(LocalDateTime.now());

        save(entity);
        // 更新历史订单状态
        List<Long> depositOrderIds = depositOrders.stream().map(MerchantDepositOrderEntity::getId).collect(Collectors.toList());
        lambdaUpdate().set(MerchantDepositOrderEntity::getRetStatus, DepositEnum.RetStatusEnum.RET_STATUS_PROCESSING.getRetStatus())
                        .in(MerchantDepositOrderEntity::getId, depositOrderIds)
                        .update();

        // 生成退回订单记录
        List<DepositRetRecordEntity> records = new ArrayList<>();
        for (MerchantDepositOrderEntity depositOrder : depositOrders) {
            DepositRetRecordEntity record = new DepositRetRecordEntity();
            record.setDepositId(entity.getId())
                    .setRetId(depositOrder.getId())
                    .setRetStatus(DepositEnum.RetStatusEnum.RET_STATUS_PROCESSING.getRetStatus())
                    .setRetChannel(depositOrder.getPayChannel())
                    .setRetAmount(depositOrder.getAmount());
            records.add(record);
        }
        depositRetRecordService.saveBatch(records);
        log.info("[转出消保金] 预处理完成, orderId: {}, merchantId: {}, amount: {}",
                entity.getId(), merchant.getId(), amount);

        return entity.getId();
    }
    /**
     * 发送事务消息
     */
    private void sendTransferMessage(Long orderId, BigDecimal amount, Long userId) throws ClientException {
        // 构建消息体
        DepositOrderTransferMqDTO dto = new DepositOrderTransferMqDTO(orderId,amount,userId);
        Message<DepositOrderTransferMqDTO> message = MessageBuilder
                .withPayload(dto)
                .setHeader("orderId", orderId)
                .build();

        // 发送事务消息
        Pair<SendReceipt, Transaction> pair = envAwareRocketMQTemplate.sendMessageInTransaction(
                DepositTransferRocketMQTemplate.class,
                RocketMQConstants.Topic.TOPIC_MERCHANT_DEPOSIT_TRANSFER,
                RocketMQConstants.Tag.TAG_MERCHANT_DEPOSIT_TRANSFER,
                message);
        log.info("[消保金转出] 发送事务消息成功-------------->>>data: {}" , JSON.toJSONString(pair) );
    }

    /**
     * 查询 处理中的订单
     * @param userId
     * @return
     */
    private MerchantDepositOrderEntity findProcessingOrderByUserId(Long userId) {
        LambdaQueryWrapper<MerchantDepositOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MerchantDepositOrderEntity::getUserId, userId)
                .eq(MerchantDepositOrderEntity::getStatus, DepositEnum.OrderStatus.PROCESSING.getStatus())
                .eq(MerchantDepositOrderEntity::getType, DepositEnum.OrderType.REFUND.getType());
        return getOne(queryWrapper,false);
    }

    private boolean executeRetDepositOrderPayment(MerchantDepositOrderEntity entity){
        List<MerchantDepositOrderEntity> depositOrders = getPayDepositOrderByUserIdAndRetStatus(entity.getUserId(),DepositEnum.RetStatusEnum.RET_STATUS_PROCESSING.getRetStatus());
        Boolean flag = true;
        for (MerchantDepositOrderEntity depositOrder : depositOrders) {
            log.info("[消保金退款]--------->>> 开始处理订单:{}",depositOrder);
            HuifuRefundDTO huifuRefundDTO = new HuifuRefundDTO();
            try {
                huifuRefundDTO.setOrderId(String.valueOf(depositOrder.getId()));
                ScanPayRefundVO payRes = baseRemoteService.huifuRefund(huifuRefundDTO);
                log.info("[消保金退款]--------->>> 调用三方支付退款结果:{}",payRes);
                if(payRes.getTradeSuccess() || "23000003".equals(payRes.getRespCode())) {
                    depositOrder.setRetStatus(DepositEnum.RetStatusEnum.RET_STATUS_PROCESSING.getRetStatus());
                    DepositRetRecordEntity retRecord = depositRetRecordService.findByRetId(depositOrder.getId());
                    retRecord.setRetSeqId(payRes.getHfSeqId());
                    updateById(depositOrder);
                    depositRetRecordService.updateById(retRecord);
                }else {
                    throw new RuntimeException("请求三方支付退款失败");
                }
            } catch (Exception e) {
                log.error("消保金订单退款异常, orderId: {},{}", depositOrder.getId(), e);
                flag = false;
                notifyError("消保金退款-原路退回异常",e.getClass().getName(),JSON.toJSONString(huifuRefundDTO), ExceptionUtils.getStackTrace(e));
            }
            log.info("[消保金退款]--------->>> 结束处理订单:{}",depositOrder.getId());
        }
        return flag;
    }

    @Override
    public Page<MerchantDepositOrderListVO> listMerchantDepositOrders(MerchantDepositOrderListQueryDTO dto) {
        // 创建分页对象
        Page<MerchantDepositOrderListVO> page = new Page<>(dto.getCurrent(), dto.getSize());
        
        // 执行分页查询
        Page<MerchantDepositOrderListVO> resultPage = this.baseMapper.selectMerchantDepositOrderPage(page, dto);
        
        // 设置描述字段
        List<MerchantDepositOrderListVO> voList = resultPage.getRecords().stream().map(vo -> {
            // 设置描述字段
            vo.setShopTypeDesc(MerchantShopTypeEnum.getByCode(vo.getShopType()).getDesc());
            vo.setPayChannelDesc(DepositEnum.PayChannelTypeEnum.getDescByType(vo.getPayChannel()));
            vo.setDepositTypeDesc(DepositEnum.OrderType.getDescByType(vo.getDepositType()));
            vo.setPayStatusDesc(DepositEnum.OrderStatus.getDescByStatus(vo.getPayStatus()));

            return vo;
        }).collect(Collectors.toList());
        
        resultPage.setRecords(voList);
        return resultPage;
    }

    private List<MerchantDepositOrderEntity> getPayDepositOrderByUserIdAndRetStatus(Long userId,Integer retStatus){
        return this.lambdaQuery().eq(MerchantDepositOrderEntity::getUserId, userId)
                .eq(MerchantDepositOrderEntity::getRetStatus, retStatus)
                .eq(MerchantDepositOrderEntity::getStatus, DepositEnum.OrderStatus.BEEN_PAY.getStatus())
                .eq(MerchantDepositOrderEntity::getType, DepositEnum.OrderType.PAY.getType())
                .list();
    }
    private void notifyError(String description,String exception,String requestParams,String stackTrace){
        try {
            String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            AlertMessage message = new AlertMessage();
            message.setModule("商户销保金")
                    .setMethod(methodName)
                    .setDescription(description)
                    .setLevel(AlertLevel.ERROR)
                    .setException(exception)
                    .setRequestParams(requestParams)
                    .setRequestParams(env)
                    .setStackTrace(stackTrace)
                    .setTimestamp(LocalDateTime.now());
            log.info("告警信息: {}", message);
            alertNotifier.notify(message);
        } catch (Exception e) {
            log.error("发送告警异常", e);
        }
    }
}
