package com.yts.yyt.merchant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.merchant.api.entity.CityPartnerChannelManagerEntity;
import com.yts.yyt.merchant.api.vo.CityPartnerChannelManagerVO;

import java.util.List;
import java.util.Map;

public interface CityPartnerChannelManagerService extends IService<CityPartnerChannelManagerEntity> {

    void insertOrUpdate(Long partnerId, Long channelManagerSysUserId);

	void insert(Long partnerId, Long channelManagerSysUserId);

	CityPartnerChannelManagerVO getByPartnerId(Long partnerId);

	Map<Long, String> getMapBySysUserIds(List<Long> sysUserIds);

	Map<Long, String> getChannelManagerMap();
}

