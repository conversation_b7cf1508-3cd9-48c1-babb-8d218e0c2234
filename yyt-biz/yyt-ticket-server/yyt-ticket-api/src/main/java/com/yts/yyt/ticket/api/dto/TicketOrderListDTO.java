package com.yts.yyt.ticket.api.dto;

import com.yts.yyt.ticket.api.enums.TicketOrderStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 票务订单列表查询参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "票务订单列表查询参数")
public class TicketOrderListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 票务订单状态
     * 1-待支付 2-待使用 3-已使用 4-已过期 5-已关闭 6-退款中 7-退票/退款
     * 当为空时，表示查询全部数据
     */
    @Schema(description = "票务订单状态（1-待支付 2-待使用 3-已使用 4-已过期 5-已关闭 6-退款中 7-退票/退款，当为空时，表示查询全部数据）字典: TICKET_ORDER_TYPE", implementation = TicketOrderStatusEnum.class)
    private Integer status;
    
    /**
     * 当前页码
     */
    @NotNull(message = "当前页码不能为空")
    @Min(value = 1, message = "当前页码不能小于1")
    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小不能小于1")
    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
} 