package com.yts.yyt.ticket.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 管理端门票订单列表返回对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理端门票订单列表返回对象")
public class TicketAdminOrderVO implements Serializable {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long id;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    /**
     * 订单来源
     */
    @Schema(description = "订单来源")
    private String orderSource;

    /**
     * 门票类型
     */
    @Schema(description = "门票类型")
    private String type;

    /**
     * 参观日期
     */
    @Schema(description = "参观日期")
    private LocalDate visitDate;

    /**
     * 门票数量
     */
    @Schema(description = "门票数量")
    private Integer ticketQuantity;

    /**
     * 预约时段
     */
    @Schema(description = "预约时段")
    private String timeSlot;

    /**
     * 单价
     */
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private BigDecimal totalAmount;

    /**
     * 买家用户姓名
     */
    @Schema(description = "买家用户姓名")
    private String name;

    /**
     * 买家手机号
     */
    @Schema(description = "买家手机号")
    private String mobile;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态: 0-待支付 1-已支付 2-已完成 3-已取消 4-已退款")
    private Integer status;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式")
    private String paymentMethod;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
} 