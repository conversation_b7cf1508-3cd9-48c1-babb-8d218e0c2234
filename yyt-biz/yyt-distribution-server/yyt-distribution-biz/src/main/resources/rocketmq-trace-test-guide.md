# RocketMQ TraceId 拦截器测试指南

## 概述

本测试套件用于验证RocketMQ TraceId拦截器的功能是否正常工作。拦截器的主要功能包括：

1. **生产者端**：自动为发送的消息添加TraceId到消息头中
2. **消费者端**：自动从消息中提取TraceId并设置到当前线程上下文中

## 测试环境准备

### 1. 确保AOP配置正确

确认以下模块的启动类已启用AOP：

```java
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@SpringBootApplication
public class YytDistributionApplication {
    // ...
}
```

### 2. 确保RocketMQ配置正确

检查`application.yml`中的RocketMQ配置：

```yaml
rocketmq:
  env: dev  # 或其他环境标识
  # 其他RocketMQ配置...
```

### 3. 确保测试Topic存在

确认RocketMQ服务器中存在以下Topic：
- `${rocketmq.env}_TP_TEST_ROCKETMQ`

## 测试接口说明

### 基础URL
```
http://localhost:7050/test/rocketmq-trace
```

### 1. 测试convertAndSend方法
```bash
curl -X POST "http://localhost:7050/test/rocketmq-trace/test-convert-send?content=测试消息1"
```

**预期结果**：
- 返回成功响应，包含TraceId
- 消费者日志显示正确提取了TraceId
- MDC和Plumelog中的TraceId一致

### 2. 测试send方法（Message对象）
```bash
curl -X POST "http://localhost:7050/test/rocketmq-trace/test-send-message?content=测试消息2"
```

**预期结果**：
- 返回成功响应
- 消费者正确处理Message对象中的TraceId

### 3. 测试延迟消息
```bash
curl -X POST "http://localhost:7050/test/rocketmq-trace/test-delay-message?content=延迟消息&delaySeconds=5"
```

**预期结果**：
- 返回成功响应，包含messageId
- 5秒后消费者收到消息并正确处理TraceId

### 4. 测试事务消息
```bash
curl -X POST "http://localhost:7050/test/rocketmq-trace/test-transaction-message?content=事务消息"
```

**预期结果**：
- 返回成功响应，包含事务ID
- 消费者正确处理事务消息中的TraceId

### 5. 测试无TraceId情况
```bash
curl -X POST "http://localhost:7050/test/rocketmq-trace/test-no-trace-id?content=无TraceId消息"
```

**预期结果**：
- 返回成功响应
- 消费者使用messageId作为fallback TraceId

### 6. 获取当前TraceId
```bash
curl -X GET "http://localhost:7050/test/rocketmq-trace/current-trace-id"
```

## 验证步骤

### 1. 启动应用
```bash
cd yyt-biz/yyt-distribution-server/yyt-distribution-biz
mvn spring-boot:run
```

### 2. 观察启动日志
查看是否有以下日志：
```
拦截器已加载: RocketMQTraceInterceptor
```

### 3. 执行测试接口
按照上述接口说明依次执行测试。

### 4. 观察日志输出

#### 生产者端日志（期望看到）：
```
DEBUG - 拦截到EnvAwareRocketMQTemplate的convertAndSend方法，参数数量：2
DEBUG - 为EnvAwareRocketMQTemplate.convertAndSend负载构建Message并添加traceId: TEST-TRACE-1642567890123
```

#### 消费者端日志（期望看到）：
```
INFO  - === RocketMQ TraceId 拦截器测试 ===
INFO  - 消息ID: 01234567890ABCDEF
INFO  - MDC中的TraceId: TEST-TRACE-1642567890123
INFO  - Plumelog中的TraceId: TEST-TRACE-1642567890123
INFO  - TraceId一致性检查: 通过
INFO  - 消息中的TraceId: TEST-TRACE-1642567890123
INFO  - TraceId提取检查: 通过
```

## 故障排查

### 1. 拦截器未生效

**症状**：生产者端没有DEBUG日志，消费者端TraceId为空

**可能原因**：
- AOP未正确配置
- 切点表达式不匹配
- 拦截器类未被Spring扫描

**解决方案**：
```java
// 检查启动类是否有AOP注解
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)

// 检查拦截器类是否有正确注解
@Aspect
@Component
@Order(1)
```

### 2. 消费者端TraceId不一致

**症状**：MDC和Plumelog中的TraceId不同

**可能原因**：
- 消费者手动设置了TraceId
- 拦截器执行顺序问题

**解决方案**：
- 移除消费者中的手动TraceId处理代码
- 检查@Order注解确保拦截器优先级

### 3. TraceId未清理

**症状**：不同消息的TraceId相同

**可能原因**：
- 拦截器的finally块未执行
- 线程池复用导致TraceId污染

**解决方案**：
- 确保拦截器的clearTraceId()方法正确执行
- 检查异常处理逻辑

## 性能测试

### 批量测试脚本
```bash
#!/bin/bash
for i in {1..100}; do
    curl -X POST "http://localhost:7050/test/rocketmq-trace/test-convert-send?content=批量测试$i"
    sleep 0.1
done
```

### 预期性能指标
- 拦截器处理时间 < 1ms
- 内存无泄漏
- TraceId正确传递率 = 100%

## 注意事项

1. **测试环境隔离**：确保测试不影响生产环境
2. **日志级别**：测试时建议将拦截器日志级别设为DEBUG
3. **消息堆积**：测试完成后清理测试Topic中的消息
4. **并发测试**：可以使用多线程同时发送消息测试并发场景

## 扩展测试

### 1. 压力测试
使用JMeter或其他工具进行大量消息发送测试。

### 2. 异常场景测试
- 消息发送失败时的TraceId处理
- 消费者抛异常时的TraceId清理
- 网络中断时的行为

### 3. 集成测试
- 与其他微服务的TraceId传递
- 与链路追踪系统的集成测试
