<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yts.yyt.distribution.mapper.DistSettleSummaryMapper">

    <!-- 分页查询业绩结算 -->
    <select id="pageQuery" resultType="com.yts.yyt.distribution.api.vo.CommissionSettlementPageVO">
        SELECT
            dss.user_id AS distributorId,
            dss.username AS distributorName,
            dul.level_name AS levelName,
            dss.share_commission_amt AS shareCommission,
            CASE 
                WHEN dss.role = 'leader' THEN 1
                ELSE 0
            END AS isTeamLeader,
            dss.selector_reward_commission AS teamLeaderIncentiveCommission,
            dss.pending_commission_amt AS pendingCommission,
            dss.withdrawable_commission_amt AS withdrawableCommission,
            dss.withdrawn_commission_amt AS withdrawnCommission,
            dss.trade_cnt AS transactionCount
        FROM dist_settle_summary dss
        LEFT JOIN dist_user_level dul ON dss.user_id = dul.user_id AND dss.role = dul.role
        WHERE dss.del_flag = 0
        and dul.del_flag = 0
        <if test="dto.distributorKeyword != null and dto.distributorKeyword != ''">
            AND dss.username LIKE CONCAT('%', #{dto.distributorKeyword}, '%')
        </if>
        <if test="dto.settlementStartTime != null">
            AND dss.create_time >= #{dto.settlementStartTime}
        </if>
        <if test="dto.settlementEndTime != null">
            AND dss.create_time &lt;= #{dto.settlementEndTime}
        </if>
        ORDER BY dss.share_commission_amt DESC, dss.create_time DESC
    </select>

    <!-- 获取业绩结算合计数据 -->
    <select id="getSummary" resultType="com.yts.yyt.distribution.api.vo.CommissionSettlementSummaryVO">
        SELECT 
            COALESCE(SUM(dss.share_commission_amt), 0) AS totalShareCommission,
            COALESCE(SUM(dss.pending_commission_amt), 0) AS totalPendingCommission,
            COALESCE(SUM(dss.withdrawable_commission_amt), 0) AS totalWithdrawableCommission,
            COALESCE(SUM(dss.withdrawn_commission_amt), 0) AS totalWithdrawnCommission
        FROM dist_settle_summary dss
        WHERE dss.del_flag = 0
        <if test="dto.distributorKeyword != null and dto.distributorKeyword != ''">
            AND dss.username LIKE CONCAT('%', #{dto.distributorKeyword}, '%')
        </if>
        <if test="dto.settlementStartTime != null">
            AND dss.create_time >= #{dto.settlementStartTime}
        </if>
        <if test="dto.settlementEndTime != null">
            AND dss.create_time &lt;= #{dto.settlementEndTime}
        </if>
    </select>

    <!-- 从订单佣金表聚合数据到汇总表 -->
    <insert id="aggregateFromOrderCommission">
        INSERT INTO dist_settle_summary (
            user_id,
            username,
            role,
            share_commission_amt,
            selector_reward_commission,
            pending_commission_amt,
            withdrawable_commission_amt,
            withdrawn_commission_amt,
            trade_cnt,
            create_time,
            update_time,
            del_flag
        )
        SELECT
            doc.user_id AS user_id,
            u.nickname AS username,
            doc.role,
            SUM(doc.commission_amt) AS share_commission_amt,
            0 AS selector_reward_commission,
            SUM(CASE 
                WHEN do.status IN (0, 1, 2) THEN doc.commission_amt 
                ELSE 0 
            END) AS pending_commission_amt,
            SUM(CASE 
                WHEN do.status = 3 THEN doc.commission_amt 
                ELSE 0 
            END) AS withdrawable_commission_amt,
            0 AS withdrawn_commission_amt,
            COUNT(DISTINCT doc.order_id) AS trade_cnt,
            doc.create_time AS create_time,
            doc.update_time AS update_time,
            0 AS del_flag
        FROM dist_order_commission doc
        INNER JOIN dist_order do ON doc.order_id = do.id
        LEFT JOIN user_info u ON doc.user_id = u.id
        WHERE doc.del_flag = 0
          AND do.del_flag = 0
          AND doc.status = 'normal'
        GROUP BY doc.user_id, doc.role, u.username
    </insert>

</mapper>

