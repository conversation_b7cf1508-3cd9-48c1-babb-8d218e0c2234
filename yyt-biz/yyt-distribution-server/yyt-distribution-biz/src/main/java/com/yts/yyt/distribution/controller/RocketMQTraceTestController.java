package com.yts.yyt.distribution.controller;

import com.alibaba.fastjson.JSON;
import com.plumelog.core.TraceId;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.rocketmq.template.DistTeamServiceFeeRetRocketMQTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * RocketMQ TraceId 拦截器测试控制器
 * 
 * 用于测试RocketMQ拦截器是否正确工作：
 * 1. 生产者端：测试TraceId是否正确添加到消息中
 * 2. 消费者端：测试TraceId是否正确从消息中提取并设置到线程上下文
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/test/rocketmq-trace")
@AllArgsConstructor
@Slf4j
public class RocketMQTraceTestController {

    private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;

    /**
     * 测试数据传输对象
     */
    @Data
    public static class TestMessageDTO {
        private String id;
        private String content;
        private Long timestamp;
        private String testType;
        
        public TestMessageDTO() {}
        
        public TestMessageDTO(String testType, String content) {
            this.id = UUID.randomUUID().toString();
            this.content = content;
            this.timestamp = System.currentTimeMillis();
            this.testType = testType;
        }
    }

    /**
     * 测试1：convertAndSend方法 - 普通对象
     * 测试拦截器是否能正确为普通对象添加TraceId
     */
    @PostMapping("/test-convert-send")
    public R<Map<String, Object>> testConvertAndSend(@RequestParam(defaultValue = "测试convertAndSend方法") String content) {
        // 手动设置TraceId到当前线程（模拟正常业务流程）
        String testTraceId = "TEST-TRACE-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);
        
        try {
            log.info("开始测试convertAndSend方法，TraceId: {}", testTraceId);
            
            TestMessageDTO testMessage = new TestMessageDTO("convertAndSend", content);
            
            // 调用EnvAwareRocketMQTemplate的convertAndSend方法
            // 拦截器应该会自动为这个消息添加TraceId
            envAwareRocketMQTemplate.convertAndSend(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ, 
                testMessage
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("traceId", testTraceId);
            result.put("message", testMessage);
            result.put("method", "convertAndSend");
            result.put("status", "发送成功");
            
            log.info("convertAndSend测试完成，消息: {}", JSON.toJSONString(testMessage));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("convertAndSend测试失败", e);
            return R.failed("发送失败: " + e.getMessage());
        } finally {
            // 清理TraceId
            TraceId.logTraceID.remove();
        }
    }

    /**
     * 测试2：send方法 - Message对象
     * 测试拦截器是否能正确为Message对象添加TraceId
     */
    @PostMapping("/test-send-message")
    public R<Map<String, Object>> testSendMessage(@RequestParam(defaultValue = "测试send方法") String content) {
        String testTraceId = "TEST-TRACE-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);
        
        try {
            log.info("开始测试send方法，TraceId: {}", testTraceId);
            
            TestMessageDTO testMessage = new TestMessageDTO("send", content);
            
            // 构建Message对象
            Message<TestMessageDTO> message = MessageBuilder
                .withPayload(testMessage)
                .setHeader("testHeader", "testValue")
                .build();
            
            // 调用EnvAwareRocketMQTemplate的send方法
            // 拦截器应该会自动为这个Message添加TraceId
            envAwareRocketMQTemplate.send(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ, 
                message
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("traceId", testTraceId);
            result.put("message", testMessage);
            result.put("method", "send");
            result.put("status", "发送成功");
            
            log.info("send测试完成，消息: {}", JSON.toJSONString(testMessage));
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("send测试失败", e);
            return R.failed("发送失败: " + e.getMessage());
        } finally {
            TraceId.logTraceID.remove();
        }
    }

    /**
     * 测试3：sendDelayMsg方法 - 延迟消息
     * 测试拦截器是否能正确为延迟消息添加TraceId
     */
    @PostMapping("/test-delay-message")
    public R<Map<String, Object>> testDelayMessage(
            @RequestParam(defaultValue = "测试延迟消息") String content,
            @RequestParam(defaultValue = "10") int delaySeconds) {
        
        String testTraceId = "TEST-TRACE-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);
        
        try {
            log.info("开始测试延迟消息，TraceId: {}, 延迟: {}秒", testTraceId, delaySeconds);
            
            TestMessageDTO testMessage = new TestMessageDTO("delayMessage", content);
            
            // 调用EnvAwareRocketMQTemplate的sendDelayMsg方法
            SendReceipt receipt = envAwareRocketMQTemplate.sendDelayMsg(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ,
                "DELAY_TEST",
                testMessage,
                Duration.ofSeconds(delaySeconds)
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("traceId", testTraceId);
            result.put("message", testMessage);
            result.put("method", "sendDelayMsg");
            result.put("delaySeconds", delaySeconds);
            result.put("messageId", receipt.getMessageId().toString());
            result.put("status", "发送成功");
            
            log.info("延迟消息测试完成，消息ID: {}", receipt.getMessageId());
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("延迟消息测试失败", e);
            return R.failed("发送失败: " + e.getMessage());
        } finally {
            TraceId.logTraceID.remove();
        }
    }

    /**
     * 测试4：sendMessageInTransaction方法 - 事务消息
     * 测试拦截器是否能正确为事务消息添加TraceId
     */
    @PostMapping("/test-transaction-message")
    public R<Map<String, Object>> testTransactionMessage(@RequestParam(defaultValue = "测试事务消息") String content) {
        String testTraceId = "TEST-TRACE-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);
        
        try {
            log.info("开始测试事务消息，TraceId: {}", testTraceId);
            
            TestMessageDTO testMessage = new TestMessageDTO("transactionMessage", content);
            
            // 构建带有事务ID的Message
            Message<TestMessageDTO> message = MessageBuilder
                .withPayload(testMessage)
                .setHeader("transactionId", System.currentTimeMillis())
                .build();
            
            // 调用EnvAwareRocketMQTemplate的sendMessageInTransaction方法
            Pair<SendReceipt, Transaction> pair = envAwareRocketMQTemplate.sendMessageInTransaction(
                DistTeamServiceFeeRetRocketMQTemplate.class,
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ,
                "TRANSACTION_TEST",
                message
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("traceId", testTraceId);
            result.put("message", testMessage);
            result.put("method", "sendMessageInTransaction");
            result.put("messageId", pair.getFirst().getMessageId().toString());
            result.put("transactionId", pair.getSecond().getTransactionId());
            result.put("status", "发送成功");
            
            log.info("事务消息测试完成，消息ID: {}, 事务ID: {}", 
                pair.getFirst().getMessageId(), pair.getSecond().getTransactionId());
            return R.ok(result);
            
        } catch (ClientException e) {
            log.error("事务消息测试失败", e);
            return R.failed("发送失败: " + e.getMessage());
        } finally {
            TraceId.logTraceID.remove();
        }
    }

    /**
     * 测试5：无TraceId情况
     * 测试当前线程没有TraceId时，拦截器的行为
     */
    @PostMapping("/test-no-trace-id")
    public R<Map<String, Object>> testNoTraceId(@RequestParam(defaultValue = "测试无TraceId") String content) {
        // 故意不设置TraceId
        log.info("开始测试无TraceId情况");
        
        try {
            TestMessageDTO testMessage = new TestMessageDTO("noTraceId", content);
            
            envAwareRocketMQTemplate.convertAndSend(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ, 
                testMessage
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("traceId", "无");
            result.put("message", testMessage);
            result.put("method", "convertAndSend");
            result.put("status", "发送成功（无TraceId）");
            
            log.info("无TraceId测试完成");
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("无TraceId测试失败", e);
            return R.failed("发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前线程的TraceId
     */
    @GetMapping("/current-trace-id")
    public R<Map<String, Object>> getCurrentTraceId() {
        String currentTraceId = TraceId.logTraceID.get();
        
        Map<String, Object> result = new HashMap<>();
        result.put("traceId", currentTraceId);
        result.put("hasTraceId", currentTraceId != null && !currentTraceId.trim().isEmpty());
        result.put("timestamp", System.currentTimeMillis());
        
        return R.ok(result);
    }
}
