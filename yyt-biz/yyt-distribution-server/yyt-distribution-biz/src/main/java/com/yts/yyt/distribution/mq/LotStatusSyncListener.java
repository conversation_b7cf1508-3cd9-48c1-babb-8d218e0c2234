//package com.yts.yyt.distribution.mq;
//
//import com.plumelog.core.TraceId;
//import com.yts.yyt.common.mq.idempotent.service.IdempotentService;
//import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
//import com.yts.yyt.common.rocketmq.util.MessageViewParseUtil;
//import com.yts.yyt.distribution.api.constants.Constant;
//import com.yts.yyt.distribution.mq.dto.LotStatusSyncMqDTO;
//import com.yts.yyt.distribution.service.DistLotInfoService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
//import org.apache.rocketmq.client.apis.message.MessageView;
//import org.apache.rocketmq.client.core.RocketMQListener;
//import org.slf4j.MDC;
//import org.springframework.stereotype.Component;
//
//import java.nio.ByteBuffer;
//import java.nio.charset.StandardCharsets;
//
///**
// * 拍品状态同步监听器
// *
// * <AUTHOR>
// * @since 2025-01-28
// */
//@Slf4j
//@Component
//@AllArgsConstructor
//@RocketMQMessageListener(
//        consumerGroup = "${rocketmq.env}_" + RocketMQConstants.ConsumerGroup.GROUP_LOT_STATUS_SYNC,
//        topic = "${rocketmq.env}_" + RocketMQConstants.Topic.TOPIC_LOT_STATUS_SYNC
//)
//public class LotStatusSyncListener implements RocketMQListener {
//
//    private final IdempotentService idempotentService;
//    private final DistLotInfoService distLotInfoService;
//
//    @Override
//    public ConsumeResult consume(MessageView messageView) {
//        ByteBuffer body = messageView.getBody();
//        String message = StandardCharsets.UTF_8.decode(body).toString();
//        log.info("[拍品状态同步]----------------->>> 执行业务,msg : {}", message);
//        LotStatusSyncMqDTO dto = MessageViewParseUtil.parsePayloadMessage(message, LotStatusSyncMqDTO.class);
//        // 幂等性校验，使用拍品ID+同步类型+操作时间作为幂等键
//        String idempotentKey = String.valueOf(dto.getLotId())
//                .concat("_")
//                .concat(String.valueOf(dto.getSyncType()))
//                .concat("_")
//                .concat(String.valueOf(dto.getOperateTime() != null ? dto.getOperateTime().toString() : System.currentTimeMillis()));
//        if(!idempotentService.tryInsertRecord(idempotentKey, Constant.MQ_IDEMPOENT_LOT_STATUS_SYNC)) {
//            log.info("[拍品状态同步]------------->>> 重复消息，msg:{}" , message);
//            return ConsumeResult.FAILURE;
//        }
//        // 根据同步类型处理不同的业务逻辑
//        distLotInfoService.syncLotStatus(dto);
//        return ConsumeResult.SUCCESS;
//    }
//}