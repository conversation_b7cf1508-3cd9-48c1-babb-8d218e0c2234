package com.yts.yyt.distribution.mq;

import com.yts.yyt.common.mq.idempotent.service.IdempotentService;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.rocketmq.util.MessageViewParseUtil;
import com.yts.yyt.distribution.api.constants.Constant;
import com.yts.yyt.distribution.mq.dto.OrderMqDTO;
import com.yts.yyt.distribution.service.DistOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(
        consumerGroup = "${rocketmq.env}_" + RocketMQConstants.ConsumerGroup.GROUP_ORDER_STATUS_CHANGED,
        topic = "${rocketmq.env}_" + RocketMQConstants.Topic.TOPIC_ORDER_STATUS_CHANGED
)
public class OrderStatusChangedListener implements RocketMQListener {

    private final IdempotentService idempotentService;
    private final DistOrderService distOrderService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        // TraceId处理已由拦截器统一处理，这里只需要专注业务逻辑
        try {
            ByteBuffer body = messageView.getBody();
            String message = StandardCharsets.UTF_8.decode(body).toString();
            log.info("[订单状态变更]----------------->>> 执行业务,msg : {}", message);
            OrderMqDTO dto = MessageViewParseUtil.parsePayloadMessage(message, OrderMqDTO.class);
            if(!idempotentService.tryInsertRecord(String.valueOf(dto.getOrderId()).concat(dto.getStatus()), Constant.MQ_IDEMPOENT_ORDER_STATUS_CHANGED)) {
                log.info("[订单状态变更]------------->>> 重复消息，msg:{}" , message);
                return ConsumeResult.FAILURE;
            }
            distOrderService.changeOrderStatusAndCalcuateGmv(dto);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("[订单状态变更] 消费失败", e);
            return ConsumeResult.FAILURE;
        }
        // TraceId清理由拦截器的finally块统一处理
    }
}
