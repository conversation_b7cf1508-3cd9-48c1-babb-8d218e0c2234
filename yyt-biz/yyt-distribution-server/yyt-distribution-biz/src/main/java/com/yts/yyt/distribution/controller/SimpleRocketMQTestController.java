package com.yts.yyt.distribution.controller;

import com.alibaba.fastjson.JSON;
import com.plumelog.core.TraceId;
import com.yts.yyt.common.rocketmq.config.EnvAwareRocketMQTemplate;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.security.annotation.Inner;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化版RocketMQ TraceId 拦截器测试控制器
 * 
 * 用于快速测试RocketMQ拦截器基本功能
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@RequestMapping("/test/simple-rocketmq")
@AllArgsConstructor
@Slf4j
public class SimpleRocketMQTestController {

    /**
     * 测试1：基础convertAndSend方法
     */
    @PostMapping("/test-basic")
    @Inner(false)
    public Map<String, Object> testBasic(@RequestParam(defaultValue = "测试消息") String content) {
        // 手动设置TraceId
        String testTraceId = "TEST-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);

        try {
            log.info("开始基础测试，TraceId: {}", testTraceId);

            // 构建简单的测试消息
            Map<String, Object> testMessage = new HashMap<>();
            testMessage.put("content", content);
            testMessage.put("timestamp", System.currentTimeMillis());
            testMessage.put("testType", "basic");

            // 发送消息
            envAwareRocketMQTemplate.convertAndSend(
                    RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ,
                    testMessage
            );

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("traceId", testTraceId);
            result.put("message", testMessage);
            result.put("status", "发送成功");

            log.info("基础测试完成");
            return result;

        } catch (Exception e) {
            log.error("基础测试失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        } finally {
            TraceId.logTraceID.remove();
        }
    }

    private final EnvAwareRocketMQTemplate envAwareRocketMQTemplate;

    /**
     * 测试2：Message对象发送
     */
    @PostMapping("/test-message")
    @Inner(false)
    public Map<String, Object> testMessage(@RequestParam(defaultValue = "测试Message") String content) {
        String testTraceId = "TEST-MSG-" + System.currentTimeMillis();
        TraceId.logTraceID.set(testTraceId);
        
        try {
            log.info("开始Message测试，TraceId: {}", testTraceId);
            
            Map<String, Object> payload = new HashMap<>();
            payload.put("content", content);
            payload.put("testType", "message");
            
            // 构建Message对象
            Message<Map<String, Object>> message = MessageBuilder
                .withPayload(payload)
                .setHeader("customHeader", "testValue")
                .build();
            
            // 发送Message
            envAwareRocketMQTemplate.send(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ, 
                message
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("traceId", testTraceId);
            result.put("status", "Message发送成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("Message测试失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        } finally {
            TraceId.logTraceID.remove();
        }
    }

    /**
     * 测试3：无TraceId情况
     */
    @PostMapping("/test-no-trace")
    @Inner(false)
    public Map<String, Object> testNoTrace(@RequestParam(defaultValue = "无TraceId测试") String content) {
        // 故意不设置TraceId
        log.info("开始无TraceId测试");
        
        try {
            Map<String, Object> testMessage = new HashMap<>();
            testMessage.put("content", content);
            testMessage.put("testType", "noTrace");
            
            envAwareRocketMQTemplate.convertAndSend(
                RocketMQConstants.Topic.TOPIC_TEST_ROCKETMQ, 
                testMessage
            );
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("traceId", "无");
            result.put("status", "无TraceId发送成功");
            
            return result;
            
        } catch (Exception e) {
            log.error("无TraceId测试失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * 获取当前TraceId状态
     */
    @GetMapping("/trace-status")
    @Inner(false)
    public Map<String, Object> getTraceStatus() {
        String currentTraceId = TraceId.logTraceID.get();
        
        Map<String, Object> result = new HashMap<>();
        result.put("traceId", currentTraceId);
        result.put("hasTraceId", currentTraceId != null && !currentTraceId.trim().isEmpty());
        result.put("timestamp", System.currentTimeMillis());
        
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Inner(false)
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "ok");
        result.put("service", "SimpleRocketMQTestController");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
