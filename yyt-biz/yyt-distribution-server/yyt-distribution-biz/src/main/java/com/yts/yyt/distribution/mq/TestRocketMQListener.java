package com.yts.yyt.distribution.mq;

import com.alibaba.fastjson.JSON;
import com.plumelog.core.TraceId;
import com.yts.yyt.common.rocketmq.constans.RocketMQConstants;
import com.yts.yyt.common.rocketmq.util.MessageViewParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * RocketMQ TraceId 拦截器测试消费者
 * 
 * 用于测试消费者端的TraceId拦截器是否正确工作：
 * 1. 验证拦截器是否正确从消息中提取TraceId
 * 2. 验证TraceId是否正确设置到当前线程上下文（MDC和Plumelog）
 * 3. 验证消费完成后TraceId是否正确清理
 * 
 * 注意：这个消费者故意不手动处理TraceId，完全依赖拦截器
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Component
@RocketMQMessageListener(
    consumerGroup = "${rocketmq.env}_" + "CG_TEST_TRACE_INTERCEPTOR",
    topic = "${rocketmq.env}_" + RocketMQConstants.Topic.TOPIC_LOT_STATUS_SYNC
)
public class TestRocketMQListener implements RocketMQListener {

    @Override
    public ConsumeResult consume(MessageView messageView) {
        try {
            // 记录消费开始时的TraceId状态
            String mdcTraceId = MDC.get("traceId");
            String plumelogTraceId = TraceId.logTraceID.get();
            
            log.info("=== RocketMQ TraceId 拦截器测试 ===");
            log.info("消息ID: {}", messageView.getMessageId());
            log.info("Topic: {}", messageView.getTopic());
            log.info("消息属性: {}", messageView.getProperties());
            
            // 检查拦截器是否正确设置了TraceId
            log.info("MDC中的TraceId: {}", mdcTraceId);
            log.info("Plumelog中的TraceId: {}", plumelogTraceId);
            
            // 验证TraceId是否一致
            boolean traceIdConsistent = (mdcTraceId != null && mdcTraceId.equals(plumelogTraceId));
            log.info("TraceId一致性检查: {}", traceIdConsistent ? "通过" : "失败");
            
            // 检查消息中的TraceId
            String messageTraceId = messageView.getProperties().get("traceId");
            log.info("消息中的TraceId: {}", messageTraceId);
            
            // 验证拦截器是否正确提取了TraceId
            boolean traceIdExtracted = (messageTraceId != null && messageTraceId.equals(mdcTraceId));
            log.info("TraceId提取检查: {}", traceIdExtracted ? "通过" : "失败");
            
            // 解析消息内容
            ByteBuffer body = messageView.getBody();
            String messageContent = StandardCharsets.UTF_8.decode(body).toString();
            log.info("消息内容: {}", messageContent);
            
            try {
                // 尝试解析为测试消息对象
                Map<String, Object> testMessage = JSON.parseObject(messageContent, Map.class);
                log.info("解析后的测试消息: {}", testMessage);
                
                String testType = (String) testMessage.get("testType");
                log.info("测试类型: {}", testType);
                
                // 根据测试类型执行不同的验证逻辑
                switch (testType) {
                    case "convertAndSend":
                        log.info("验证convertAndSend方法的TraceId传递");
                        break;
                    case "send":
                        log.info("验证send方法的TraceId传递");
                        break;
                    case "delayMessage":
                        log.info("验证延迟消息的TraceId传递");
                        break;
                    case "transactionMessage":
                        log.info("验证事务消息的TraceId传递");
                        break;
                    case "noTraceId":
                        log.info("验证无TraceId情况的处理");
                        // 对于无TraceId的情况，拦截器应该使用messageId作为fallback
                        if (mdcTraceId != null && mdcTraceId.equals(messageView.getMessageId().toString())) {
                            log.info("无TraceId情况处理正确：使用messageId作为TraceId");
                        } else {
                            log.warn("无TraceId情况处理异常：期望使用messageId，实际TraceId: {}", mdcTraceId);
                        }
                        break;
                    default:
                        log.info("未知测试类型: {}", testType);
                }
                
            } catch (Exception e) {
                log.warn("解析测试消息失败，可能不是测试消息: {}", e.getMessage());
            }
            
            // 模拟业务处理
            log.info("开始模拟业务处理...");
            Thread.sleep(100); // 模拟处理时间
            log.info("业务处理完成");
            
            // 再次检查TraceId是否仍然存在（应该存在，因为拦截器会在finally中清理）
            String finalMdcTraceId = MDC.get("traceId");
            String finalPlumelogTraceId = TraceId.logTraceID.get();
            log.info("业务处理后的MDC TraceId: {}", finalMdcTraceId);
            log.info("业务处理后的Plumelog TraceId: {}", finalPlumelogTraceId);
            
            log.info("=== 消息消费成功 ===");
            return ConsumeResult.SUCCESS;
            
        } catch (Exception e) {
            log.error("消息消费失败", e);
            
            // 即使出现异常，也要记录TraceId状态
            String errorMdcTraceId = MDC.get("traceId");
            String errorPlumelogTraceId = TraceId.logTraceID.get();
            log.error("异常时的MDC TraceId: {}", errorMdcTraceId);
            log.error("异常时的Plumelog TraceId: {}", errorPlumelogTraceId);
            
            return ConsumeResult.FAILURE;
        }
        // 注意：这里不手动清理TraceId，完全依赖拦截器的finally块清理
    }
}
