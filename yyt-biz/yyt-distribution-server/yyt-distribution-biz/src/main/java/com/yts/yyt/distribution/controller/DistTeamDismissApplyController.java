package com.yts.yyt.distribution.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.net.HttpHeaders;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.excel.annotation.ResponseExcel;
import com.yts.yyt.common.log.annotation.SysLog;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.distribution.api.constants.TeamDismissStatusEnum;
import com.yts.yyt.distribution.api.dto.DistTeamDismissApplyDTO;
import com.yts.yyt.distribution.api.dto.DistTeamDismissApplyQueryDTO;
import com.yts.yyt.distribution.api.dto.DistTeamDismissAuditDTO;
import com.yts.yyt.distribution.api.vo.DistTeamDismissApplyExportVO;
import com.yts.yyt.distribution.api.vo.DistTeamDismissApplyListVO;
import com.yts.yyt.distribution.api.vo.DistTeamDismissApplyVO;
import com.yts.yyt.distribution.api.vo.DistTeamDismissApplyCountVO;
import com.yts.yyt.distribution.service.DistTeamDismissApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队解散申请控制器
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/team/dismiss")
@Tag(description = "团队解散申请", name = "团队解散申请")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DistTeamDismissApplyController extends BaseController {
    
    private final DistTeamDismissApplyService distTeamDismissApplyService;

    /**
     * 申请解散团队
     * @return 申请结果
     */
    @PostMapping("/apply")
    @SysLog("申请解散团队")
    @Operation(summary = "移动端-申请解散团队", description = "团长申请解散团队")
    public R<Boolean> applyDismiss() {
        Long userId = SecurityUtils.getUser().getId();
        Boolean result = distTeamDismissApplyService.applyDismiss(userId);
        return R.ok(result);
    }

    /**
     * 分页查询团队解散申请列表
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @Operation(summary = "后台管理-分页查询团队解散申请列表", description = "分页查询团队解散申请列表 -team_dismiss_page")
    @HasPermission("team_dismiss_page")
    public R<Page<DistTeamDismissApplyListVO>> page(@RequestBody DistTeamDismissApplyQueryDTO queryDTO) {
        Page<DistTeamDismissApplyListVO> page = distTeamDismissApplyService.page(queryDTO);
        return R.ok(page);
    }

    /**
     * 查询各状态下数量统计
     * @param queryDTO 查询条件
     * @return 数量统计结果
     */
    @PostMapping("/count")
    @Operation(summary = "后台管理-查询各状态下数量统计", description = "查询各状态下数量统计 -team_dismiss_count")
    @HasPermission("team_dismiss_count")
    public R<DistTeamDismissApplyCountVO> getCount(@RequestBody DistTeamDismissApplyQueryDTO queryDTO) {
        DistTeamDismissApplyCountVO count = distTeamDismissApplyService.getCount(queryDTO);
        return R.ok(count);
    }

    /**
     * 审核团队解散申请
     * @param auditDTO 审核信息
     * @return 审核结果
     */
    @PostMapping("/audit")
    @SysLog("审核团队解散申请")
    @Operation(summary = "后台管理-审核团队解散申请", description = "后台审核团队解散申请 -team_dismiss_audit")
    @HasPermission("team_dismiss_audit")
    public R<Boolean> audit(@Valid @RequestBody DistTeamDismissAuditDTO auditDTO) {
        Boolean result = distTeamDismissApplyService.audit(auditDTO);
        return R.ok(result);
    }

    /**
     * 导出团队解散申请列表
     * @param queryDTO 查询条件
     * @return 导出数据
     */
    @PostMapping("/export")
    @ResponseExcel
    @Operation(summary = "后台管理-导出团队解散申请列表", description = "导出团队解散申请列表 -team_dismiss_export")
    @HasPermission("team_dismiss_export")
    @SysLog("导出团队解散申请列表")
    public List<DistTeamDismissApplyExportVO> export(@RequestBody DistTeamDismissApplyQueryDTO queryDTO) {
        queryDTO.setCurrent(1L);
        queryDTO.setSize(Long.MAX_VALUE);
        Page<DistTeamDismissApplyListVO> page = distTeamDismissApplyService.page(queryDTO);
        return page.getRecords().stream().map(item -> {
            DistTeamDismissApplyExportVO vo = new DistTeamDismissApplyExportVO();
            BeanUtil.copyProperties(item, vo);
            vo.setStatus(TeamDismissStatusEnum.getDescBySatus(item.getStatus()));
            return vo;
        }).toList();
    }
}

