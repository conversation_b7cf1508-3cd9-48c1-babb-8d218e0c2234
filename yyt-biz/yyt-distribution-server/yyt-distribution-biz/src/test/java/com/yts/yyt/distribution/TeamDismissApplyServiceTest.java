package com.yts.yyt.distribution;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.distribution.api.dto.DistTeamDismissApplyQueryDTO;
import com.yts.yyt.distribution.api.dto.DistTeamDismissAuditDTO;
import com.yts.yyt.distribution.api.vo.DistTeamDismissApplyListVO;
import com.yts.yyt.distribution.service.DistTeamDismissApplyService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class TeamDismissApplyServiceTest extends BaseSpringTest{

    @Autowired
    private DistTeamDismissApplyService teamDismissApplyService;;

    @Test
    public void test() {
        Long userId = 1689165826172342272L;
        DistTeamDismissAuditDTO audit  = new DistTeamDismissAuditDTO();
        audit.setId(1937004435264978945L);
        audit.setStatus("reject");
        audit.setRemark("remark");
        teamDismissApplyService.audit(audit);
    }


    @Test
    public void test_page(){
        DistTeamDismissApplyQueryDTO queryDTO = new DistTeamDismissApplyQueryDTO();
        queryDTO.setCurrent(1L);
        queryDTO.setSize(10L);
        queryDTO.setQueryKeyWords("1");
        Page<DistTeamDismissApplyListVO> page = teamDismissApplyService.page(queryDTO);
        System.out.println(JSON.toJSONString(page));
    }



}
