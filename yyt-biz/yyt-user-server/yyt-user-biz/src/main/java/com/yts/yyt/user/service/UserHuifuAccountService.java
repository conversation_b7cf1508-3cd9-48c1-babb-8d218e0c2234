package com.yts.yyt.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.user.api.dto.UserHuifuAccountEditDTO;
import com.yts.yyt.user.api.dto.UserHuifuAccountSaveDTO;
import com.yts.yyt.user.api.vo.UserHuifuAccountQueryVO;
import com.yts.yyt.user.api.entity.UserHuifuAccount;

public interface UserHuifuAccountService extends IService<UserHuifuAccount> {
	/**
	 * 新增数据
	 *
	 * @param dto 请求参数对象
	 * @return 新增结果
	 */
	Boolean add(UserHuifuAccountSaveDTO dto);

	/**
	 * 修改信息
	 *
	 * @param dto 请求参数对象
	 * @return 修改结果
	 */
	Boolean edit(UserHuifuAccountEditDTO dto);

	/**
	 * 根据用户id 或者 系统id 或者 商家id查询用户信息
	 *
	 * @param id id
	 * @return 查询结果
	 */
	UserHuifuAccountQueryVO getByUserIdOrSysUserIdOrMerchantId(Long id,String huifuId);
}
