package com.yts.yyt.user.controller;

import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.common.tencent.dto.IMAccountImportDTO;
import com.yts.yyt.common.tencent.vo.AccountCheckResultVO;
import com.yts.yyt.user.api.dto.UserIdsRequest;
import com.yts.yyt.user.api.vo.MultiAccountImportResultVO;
import com.yts.yyt.user.service.UserImInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(description = "IM管理", name = "IM管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class UserIMController extends BaseController {

    private final UserImInfoService userImInfoService;



    /**
     * 获取用户凭证
     *
     * @return R 用户信息
     */
    @PostMapping("/im/getUserSign")
    @Operation(summary = "获取用户凭证", description = "获取用户凭证")
    public R<String> getUserSign() {
       Long userId = SecurityUtils.getUser().getId();

      return R.ok(userImInfoService.getUserSign(userId));
    }

    /**
     * 查询用户IM状态
     *
     * @param request 用户ID列表请求
     * @return R 用户IM状态列表
     */
    @PostMapping("/im/checkUserStatus")
    @Operation(summary = "查询用户IM状态", description = "查询用户IM状态")
    public R<List<AccountCheckResultVO>> checkUserStatus(@RequestBody UserIdsRequest request) {
        return R.ok(userImInfoService.checkUserStatus(request.getUserIds().toArray(new String[0])));
    }

    /**
     * 设置用户IM账号失效
     *
     * @param userId 用户ID
     * @return R 设置结果
     */
    @PostMapping("/im/setUserImInvalid")
    @Operation(summary = "设置用户IM账号失效", description = "设置用户IM账号失效")
    public R<Boolean> setUserImInvalid(@RequestParam("userId") Long userId) {
        boolean result = userImInfoService.setUserImInvalid(userId);
        if (!result) {
            return R.failed("设置用户IM账号失效失败");
        }
        return R.ok(true);
    }

	/**
	 * 批量导入IM账号
	 *
	 * @param request 账号导入请求
	 * @return R 导入结果
	 */
	@PostMapping("/im/multiAccountImport")
	@Operation(summary = "批量导入IM账号", description = "批量导入IM账号")
	public R<MultiAccountImportResultVO> multiAccountImport(@RequestBody IMAccountImportDTO request) {
		MultiAccountImportResultVO result = userImInfoService.multiAccountImport(request.getAccountList());
		if (!result.isSuccess()) {
			return R.failed("批量导入IM账号失败，失败账号：" + String.join(",", result.getFailAccounts()));
		}
		return R.ok(result);
	}
}
