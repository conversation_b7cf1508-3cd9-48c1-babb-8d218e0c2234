package com.yts.yyt.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.S3Object;
import com.google.common.collect.Lists;
import com.yts.yyt.admin.api.dto.SysFileLogDTO;
import com.yts.yyt.admin.api.entity.SysDictItem;
import com.yts.yyt.admin.api.entity.SysFile;
import com.yts.yyt.admin.api.feign.RemoteDictService;
import com.yts.yyt.admin.api.feign.RemoteFileService;
import com.yts.yyt.admin.api.feign.RemoteParamService;
import com.yts.yyt.admin.api.vo.SysFileVO;
import com.yts.yyt.admin.api.vo.UploadFileVO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.file.core.FileProperties;
import com.yts.yyt.common.file.core.FileTemplate;
import com.yts.yyt.common.file.oss.service.OssTemplate;
import com.yts.yyt.common.security.util.SecurityUtils;
import com.yts.yyt.goods.api.constants.GoodsConstant;
import com.yts.yyt.goods.api.exception.GoodsBizErrorEnum;
import com.yts.yyt.goods.api.exception.GoodsException;
import com.yts.yyt.user.api.constant.enums.DictEnum;
import com.yts.yyt.user.api.dto.CosPropertiesDTO;
import com.yts.yyt.user.api.dto.GoodsZipInfoSaveDTO;
import com.yts.yyt.user.api.constant.enums.FileCompressEnum;
import com.yts.yyt.user.api.exception.UserException;
import com.yts.yyt.user.api.vo.FileConfParamsVO;
import com.yts.yyt.user.service.CosProcessor;
import com.yts.yyt.user.service.SysFileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.amazonaws.Protocol.HTTPS;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2019-06-18 17:18:42
 */
@Slf4j
@Service
@AllArgsConstructor
public class SysFileServiceImpl implements SysFileService {

    private final RemoteDictService remoteDictService;

    private final RemoteFileService remoteFileService;

    private final FileTemplate fileTemplate;

    private final FileProperties properties;
    private final CosProcessor cosProcessor;

	private final RedissonClient redissonClient;

	private final RemoteParamService remoteParamService;

	private static final long MAX_FILE_BASE = 30L;
	private static final long MAX_FILE_SIZE = MAX_FILE_BASE * 1024 * 1024; // 30MB

	private static final String GOODS_ZIP_LOCK_KEY = "goods:zip:lock:";

    /**
     * 上传文件
     *
     * @param file    文件流
     * @param dir     文件夹
     * @param groupId 分组ID
     * @param type    类型
     * @return
     */
    @Override
    public R uploadFile(MultipartFile file, String dir, Long groupId, String type) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        // 显示文件地址
        String fileShowUrl = String.format("/user/file/show?fileName=%s", fileName);

        try (InputStream inputStream = file.getInputStream()) {
			log.info("[上传文件] 桶名:{}",properties.getBucketName());
			log.info("[上传文件] 路径:{}",dir);
			log.info("[上传文件] 文件名:{}",fileName);
            fileTemplate.putObject(properties.getBucketName(), dir, fileName, inputStream, file.getContentType());
            // 文件管理数据记录,收集管理追踪文件
            fileLog(file, dir, fileName, groupId, type);

            // 处理OSS文件完整路径(不使用getObjectURL,每次会调用OSS接口获取URL)
            // fileShowUrl =((OssTemplate)fileTemplate).getObjectURL(properties.getBucketName(), fileName, 1);
            fileShowUrl = fileTemplate instanceof OssTemplate ? toOssShowUrl(dir, fileName) : fileShowUrl;
        } catch (Exception e) {
            log.error("[上传文件] 上传失败:{}", e.getMessage(), e);
            try {
                // 上传失败尝试删除对应文件
                fileTemplate.removeObject(properties.getBucketName(), fileName);
            } catch (Exception ignored) {
            }
            return R.failed(e.getLocalizedMessage());
        }

        // 返回数据
        return R.ok(new UploadFileVO(properties.getBucketName(), fileName, fileShowUrl));
    }

	public static void main(String[] args) {
		System.out.println(StrUtil.format("文件不得超过{}M", MAX_FILE_BASE));
	}

	/**
	 * 商品图片zip包解压上传
	 * @param file zip包
	 * @param dir dir
	 * @return
	 */
	@Override
	public R<List<UploadFileVO>> uploadZipFile(MultipartFile file, String dir) {
		//判断文件大小
		if (null != file && file.getSize() > MAX_FILE_SIZE) {
			log.info("【uploadZipFile】文件大小超限，文件大小：{}", file.getSize());
			return R.failed(StrUtil.format("文件不得超过{}M",MAX_FILE_BASE));
		}
		List<UploadFileVO> resultList = Lists.newArrayList();
		if (StrUtil.isBlank(dir)) {
			dir = "goods/"+DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
		}
		try (ZipArchiveInputStream zipInputStream = new ZipArchiveInputStream(file.getInputStream())){
			ZipArchiveEntry entry;
			Path tempDir = Files.createTempDirectory("unzipped"); // 创建一个临时目录来解压文件
			List<FileInfoTemp> imageFileList = Lists.newArrayList();
			while ((entry = zipInputStream.getNextEntry()) != null) {
				log.info("文件名：{}",entry.getName());
				Path filePath = tempDir.resolve(entry.getName());

				// 检查是否是目录，如果是则创建它
				if (entry.isDirectory()) {
					Files.createDirectories(filePath);
				} else if(!entry.getName().endsWith(".xlsx")){
					// 否则，创建父目录并写入文件内容
					writeTempFile(zipInputStream, filePath);
					// 拼接文件名 ，通过文件名能关联出商品
					String[] parts = entry.getName().split("/");
					String directory = parts[parts.length - 2]; // 获取倒数第二个元素：目录名（商品名_商品编码）
					String[] nameCodeArr = directory.split("_");
					String goodsName = nameCodeArr[0];
					String goodsNo = nameCodeArr[1];
					String fileName = parts[parts.length - 1]; // 获取最后一个元素：文件名
					fileName = directory + "_" + fileName;
					InputStream inputStream = Files.newInputStream(filePath);
					String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);

					imageFileList.add(new FileInfoTemp(fileName, fileType, inputStream,goodsName,goodsNo));
				}
			}
			//处理文件集合
			if (CollUtil.isNotEmpty(imageFileList)) {
				Map<String, FileInfoTemp> fileMap = imageFileList.parallelStream().collect(Collectors.toMap(FileInfoTemp::getFileName, item -> item, (o1, o2) -> o2));
				//先删除重复的文件
				log.info("【uploadZipFile】 all文件名：{}", JSONObject.toJSONString(fileMap.keySet()));
				R<Integer> num = remoteFileService.removeByNames(fileMap.keySet());
				log.info("删除记录数：{}",JSONObject.toJSONString(num));
				final String lastDir = dir;
				resultList = fileMap.values().parallelStream().map(item -> {
					// 上传OSS
					try {
						fileTemplate.putObject(properties.getBucketName(), lastDir, item.getFileName(), item.getInputStream(), item.getFileType());
					} catch (Exception e) {
						log.error("[uploadZipFile] 文件：{}，上传异常：", item.getFileName(), e);
					}
					// 文件管理数据记录,收集管理追踪文件
					fileInfoSave(item.getInputStream(), lastDir, item.getFileName(), null, item.getFileType(), item.getGoodsNo());
					return new UploadFileVO(properties.getBucketName(), item.getFileName(), toOssShowUrl(lastDir, item.getFileName()));
				}).toList();
			}
			// 清理临时目录
			Files.walk(tempDir)
					.map(Path::toFile)
					.sorted((o1, o2) -> -o1.compareTo(o2)) // 先删除文件，再删除目录
					.forEach(File::delete);
		}catch (Exception e) {
			log.error("[uploadZipFile] 文件上传异常：",e);
			return R.failed(e.getLocalizedMessage());
		}
		// 返回数据
		return R.ok(resultList);
	}

	/**
	 * 将解压后的文件写入临时文件夹
	 * @param zipInputStream
	 * @param filePath
	 * @throws IOException
	 */
	private static void writeTempFile(ZipArchiveInputStream zipInputStream, Path filePath) throws IOException {
		Path parentDir = filePath.getParent();
		if (parentDir != null && !Files.exists(parentDir)) {
			Files.createDirectories(parentDir);
		}
		try (BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(filePath))) {
			byte[] buffer = new byte[1024];
			int len;
			while ((len = zipInputStream.read(buffer)) > -1) {
				bos.write(buffer, 0, len);
			}
		}
	}

	@Override
    public R uploadFile(MultipartFile file, String uploadType) {
        FileConfParamsVO confParams = findFileConfParams(uploadType);
        return uploadFile(file, confParams.getDir(), confParams.getGroupId(), null);
    }

    /**
     * 文件管理数据记录，收集管理追踪文件
     *
     * @param file     上传的文件格式
     * @param dir      文件夹
     * @param fileName 文件名
     * @param groupId  文件组ID
     * @param type     文件类型
     */
    @SneakyThrows
    private void fileLog(MultipartFile file, String dir, String fileName, Long groupId, String type) {
        // 创建SysFile对象并设置相关属性
        SysFileLogDTO sysFile = new SysFileLogDTO();
        sysFile.setFileName(fileName);

        // 对原始文件名进行编码转换
        String oFilename = StrUtil.blankToDefault(file.getOriginalFilename(), fileName);
        String originalFilename = new String(oFilename.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        sysFile.setHash(SecureUtil.md5(file.getInputStream()));
        sysFile.setBucketName(properties.getBucketName());
        sysFile.setOriginal(originalFilename);
        sysFile.setFileSize(file.getSize());
        sysFile.setGroupId(groupId);
        sysFile.setType(type);
        sysFile.setDir(dir);
        sysFile.setCreateBy("default");
        if (ObjectUtil.isNotEmpty(SecurityUtils.getUser())) {
            sysFile.setCreateBy(SecurityUtils.getUser().getUsername());
        }

        // 保存SysFile对象
        remoteFileService.saveFileLog(sysFile);
    }

	/**
	 * 文件管理数据记录，收集管理追踪文件
	 *
	 * @param dir      文件夹
	 * @param fileName 文件名
	 * @param groupId  文件组ID
	 * @param type     文件类型
	 */
	@SneakyThrows
	private void fileInfoSave(InputStream inputStream, String dir, String fileName, Long groupId, String type, String remark) {
		SysFileLogDTO sysFile = buildSysFileDto(inputStream, dir, fileName, groupId, type, remark);
		// 保存SysFile对象
		remoteFileService.saveFileLog(sysFile);
	}

	@NotNull
	private SysFileLogDTO buildSysFileDto(InputStream inputStream, String dir, String fileName, Long groupId, String type, String remark) {
		// 创建SysFile对象并设置相关属性
		SysFileLogDTO sysFile = new SysFileLogDTO();
		sysFile.setFileName(fileName);
		// 对原始文件名进行编码转换
		if (ObjUtil.isNotNull(inputStream)) {
			sysFile.setHash(SecureUtil.md5(inputStream));
		}
		sysFile.setBucketName(properties.getBucketName());
		sysFile.setOriginal(fileName);
		sysFile.setFileSize(null);
		sysFile.setGroupId(groupId);
		sysFile.setType(type);
		sysFile.setDir(dir);
		sysFile.setCreateBy("default");
		sysFile.setRemark(remark);
		if (ObjectUtil.isNotEmpty(SecurityUtils.getUser())) {
			sysFile.setCreateBy(SecurityUtils.getUser().getUsername());
		}
		return sysFile;
	}

	@Override
    public void getFile(String fileName, HttpServletResponse response) {
        R<SysFileVO> fileData = remoteFileService.getFileData(fileName);
        if (fileData.isOk() && ObjectUtil.isNotEmpty(fileData.getData())) {
            try (S3Object s3Object = fileTemplate.getObject(fileData.getData().getBucketName(), fileData.getData().getDir(), fileName)) {
                response.setContentType("application/octet-stream; charset=UTF-8");
                response.addHeader("Hash", fileData.getData().getHash());
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(fileData.getData().getOriginal()));
                IoUtil.copy(s3Object.getObjectContent(), response.getOutputStream());
            } catch (Exception e) {
                log.error("文件读取异常: {}", e.getLocalizedMessage());
            }
        }
    }

//	@Override
//	public void saveGoodsZipInfo(GoodsZipInfoSaveDTO saveDTO) {
//		Long userId = SecurityUtils.getUser().getId();
//		long startTime = System.currentTimeMillis();
//		// 获取分布式锁
//		String lockKey = GOODS_ZIP_LOCK_KEY + userId;
//		RBucket<String> bucket = redissonClient.getBucket(lockKey);
//		boolean isSet = false;
//		try {
//			// 尝试获取锁，最多等待3秒，10秒后自动释放
//			isSet = bucket.trySet(saveDTO.getFileName(), 120, TimeUnit.SECONDS);
//			if (!isSet) {
//				log.info("上传压缩包处理，当前用户存在, 文件名：{}", saveDTO.getFileName());
//				throw GoodsException.build(GoodsErrorEnum.GOODS_ZIP_UPLOAD_BUSY_ERROR);
//			}
//			// zip处理
//			saveGoodsZipInfo(saveDTO, bucket);
//		} catch (Exception e) {
//			log.error("上传压缩包处理异常, userId:{}, 文件名:{}", userId, saveDTO.getFileName(), e);
//			Thread.currentThread().interrupt();
//			throw GoodsException.build(GoodsErrorEnum.GOODS_OUT_SYSTEM_ERROR);
//		} finally {
//			log.info("上传压缩包处理完成, 文件名：{}，耗时:{}ms", saveDTO.getFileName(),System.currentTimeMillis() - startTime);
//		}
//	}

	@Override
	public void saveFolderInfo(GoodsZipInfoSaveDTO saveDTO) {
		//1、查询文件是否已经存在
		R<SysFileVO> dbZipFileR = remoteFileService.getFileData(saveDTO.getFileName());
		if (ObjUtil.isNull(dbZipFileR) || !dbZipFileR.isOk() ) {
			log.info("[saveFolderInfo] 调用admin系统异常");
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_OUT_SYSTEM_ERROR);
		}
		SysFileVO dbZipFile = dbZipFileR.getData();
		if (ObjUtil.isNotNull(dbZipFile)) {
			log.info("【saveFolderInfo】 文件夹名称已存在");
			throw GoodsException.build(GoodsBizErrorEnum.PARAMS_ERROR);
		}
		boolean isDecompressed=false;
		if (ObjUtil.isNull(dbZipFile)) {
			//2、保存记录，待处理
			fileInfoSave(null, saveDTO.getDir(), saveDTO.getFileName(), null, saveDTO.getType(), FileCompressEnum.WAITDECOMPRESS.getCode());
			dbZipFile = remoteFileService.getFileData(saveDTO.getFileName()).getData();
		}else if(dbZipFile.getRemark().startsWith(FileCompressEnum.DECOMPRESSED.getCode())){  //待解压
			// 不需要再处理保存图片信息
			isDecompressed = true;
		}

		//3、异步查询解压结果 以及 处理文件
		if (!isDecompressed) {
//			CompletableFuture.runAsync(()->{
			//获取cos上的文件夹信息  "cytest/第二批100件-cy2", "cytest/"
			String dirAndFileName = saveDTO.getDir() + "/" + saveDTO.getFileName();
			getFileFromCosAndSave(dirAndFileName);
			// 更新zip文件记录状态：compressedZip
			dbZipFile.setRemark(FileCompressEnum.DECOMPRESSED.getCode());
			remoteFileService.updateFile(dbZipFile);
//			});
		}
	}

	public String preHandleCheck(){
		R<List<SysFile>> failFileListR = remoteFileService.getUserFailData(SecurityUtils.getUser().getUsername());
		if (ObjUtil.isNull(failFileListR) || !failFileListR.isOk() ) {
			log.info("[saveGoodsZipInfo] 调用admin系统异常");
		}
		List<SysFile> failFileList = failFileListR.getData();
		log.info("[saveGoodsZipInfo] 用户上次失败数据：{}",JSONObject.toJSONString(failFileList));
		if (CollUtil.isNotEmpty(failFileList)) {
			SysFile sysFile = failFileList.get(0);
			//删除，并提醒用户
			remoteFileService.removeByNames(Lists.newArrayList(sysFile.getFileName()));
			String message = "本次导入处理中，注意：上次导入的压缩包（"+sysFile.getFileName()+"）处理失败，请重新导入";
			log.info("[saveGoodsZipInfo] ：{}",message);
			return message;
		}
		return "";
	}

	@Override
	public String saveGoodsZipInfo(GoodsZipInfoSaveDTO saveDTO) {
		log.info("【saveGoodsZipInfo】保存商品图片压缩包信息，入参：{}",JSONObject.toJSONString(saveDTO));
		String message = "";
		String dirAndFileName = saveDTO.getDir() + "/" + saveDTO.getFileName(); // 解压cos上的zip包  "cytest/第二批100件-cy2.zip", "cytest/"
		//0、如果当前用户之前有解压处理失败的压缩包文件，先删除，再处理现在的压缩包（异步查询解压任务，发现解压失败的数据）
		message = preHandleCheck();
		//1、查询文件是否已经存在
		R<SysFileVO> dbZipFileR = remoteFileService.getFileData(saveDTO.getFileName());
		if (ObjUtil.isNull(dbZipFileR) || !dbZipFileR.isOk() ) {
			log.info("[saveGoodsZipInfo] 调用admin系统异常");
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_OUT_SYSTEM_ERROR);
		}
		SysFileVO dbZipFile = dbZipFileR.getData();
		log.info("【saveGoodsZipInfo】保存商品图片压缩包信息，dbRecord：{}",JSONObject.toJSONString(dbZipFile));
		//2、保存压缩包信息、并解压
		String jobId="";
		boolean isDecompressed= false;
		try {
			if (ObjUtil.isNull(dbZipFile)) {
				//保存zip记录：待解压
				fileInfoSave(null, saveDTO.getDir(), saveDTO.getFileName(), null, saveDTO.getType(), FileCompressEnum.WAITDECOMPRESS.getCode());
				dbZipFile = remoteFileService.getFileData(saveDTO.getFileName()).getData();
				jobId = cosProcessor.createFileProcessUncompressJob(dirAndFileName, saveDTO.getDir());
				// 更新zip文件信息：解压中
				updateDecompressFile(dbZipFile, jobId, FileCompressEnum.DECOMPRESSING.getCode());
			}else if(dbZipFile.getRemark().startsWith(FileCompressEnum.WAITDECOMPRESS.getCode())){  //待解压
				jobId = cosProcessor.createFileProcessUncompressJob(dirAndFileName, saveDTO.getDir());
				// 更新zip文件信息：解压中
				updateDecompressFile(dbZipFile, jobId, FileCompressEnum.DECOMPRESSING.getCode());
			}else if(dbZipFile.getRemark().startsWith(FileCompressEnum.DECOMPRESSING.getCode())){  //待解压
				jobId = dbZipFile.getRemark().split("_")[1];
			}else {
				jobId = dbZipFile.getRemark().split("_")[1];
				isDecompressed = true;
			}
			if (StrUtil.isBlank(jobId)) {
				throw new UserException("解压任务创建失败");
			}
		} catch (Exception e) {
			log.info("【saveGoodsZipInfo】解压任务创建失败：",e);
			// 更新zip文件记录状态：compressedZip
			updateDecompressFile(dbZipFile.getId(), jobId, FileCompressEnum.DECOMPRESSFAIL.getCode());
			remoteFileService.removeByNames(Lists.newArrayList(dbZipFile.getFileName()));
			throw new UserException("压缩包处理失败，请重试或联系管理员");
		}
		//3、异步查询解压结果 以及 处理文件
		String jId = jobId;
		Long fileId = dbZipFile.getId();
		if (!isDecompressed) {
			CompletableFuture.runAsync(()->{
				log.info("【saveGoodsZipInfo】异步处理压缩包图片信息，文件名：{}，压缩包记录id：{}",dirAndFileName, fileId);
				decompressHandle(dirAndFileName, jId, fileId);
				log.info("【saveGoodsZipInfo】异步处理压缩包图片信息-结束，文件名：{}，压缩包记录id：{}",dirAndFileName, fileId);
			});
		}
		return message;
	}

	private void decompressHandle(String dirAndFileName, String jobId, Long fileId) {
		try {
			int count = 1;
			boolean queryResult = false;
			do {
				queryResult = cosProcessor.describeFileProcessJob(jobId);
				try {
					Thread.sleep(TimeUnit.SECONDS.toMillis(3));
				} catch (InterruptedException e) {
					log.info("【decompressHandle】异步查询解压任务执行睡眠异常：",e);
				}
				count++;
			}while (!queryResult && count <= 20);
			//如果已经解压成功，获取整个目录结构（文件名称等）
			if (!queryResult) {
				//解压不成功
				log.info("【decompressHandle】异步查询解压任务：解压失败");
				throw new Exception("【decompressHandle】异步查询解压任务：解压失败");
			}
			getFileFromCosAndSave(dirAndFileName);
			// 更新zip文件记录状态：compressedZip
			updateDecompressFile(fileId, jobId, FileCompressEnum.DECOMPRESSED.getCode());
		} catch (Exception e) {
			log.info("【decompressHandle】异步查询解压任务：",e);
			// 更新zip文件记录状态：compressedZip
			updateDecompressFile(fileId, jobId, FileCompressEnum.DECOMPRESSFAIL.getCode());
		}
	}

	private void getFileFromCosAndSave(String dirAndFileName) {
		//0、获取工作流id
		boolean isStoreGoodsImg = false;
		String workflowId="";
		if (GoodsConstant.store_goods_dir.equals(dirAndFileName.split("/")[0])) {
			isStoreGoodsImg = true;
			R<String> value = remoteParamService.getByKey(GoodsConstant.IMG_WORKFLOW_ID);
			if (value.isOk()) {
				workflowId = value.getData();
			}
		}
		//1、获取整个压缩包的目录结构
		List<String> fileList = cosProcessor.getBucketObjectList(dirAndFileName.replace(".zip", ""));
		List<SysFileLogDTO> sysFileList = Lists.newArrayList();
		for (String filePath : fileList) {
			String[] parts = filePath.split("/");
			if (parts.length<2) {
				continue;
			}
			String fileName = parts[parts.length - 1]; // 获取最后一个元素：文件名
			//过滤掉压缩图片
			if (fileName.contains("_320.") || fileName.contains("_480.") || fileName.contains("_720.")) {
				continue;
			}
			String fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
			String directory = parts[parts.length - 2]; // 获取倒数第二个元素：目录名（商品名_商品编码）
			String goodsNo;
			if (directory.contains("_")) {
				//获取倒数第二个元素：目录名（商品名_商品编码）
				goodsNo = directory.split("_")[1];
			}else{
				//获取倒数第二个元素：目录名（只有商品编码）
				goodsNo = directory;
			}
			//保存图片信息
			sysFileList.add(buildSysFileDto(null, filePath.substring(0,filePath.lastIndexOf("/")), fileName, null, fileType, goodsNo));
			//如果是藏品图片，则触发工作流
			if (isStoreGoodsImg) {
				cosProcessor.triggerWorkflow(workflowId, filePath);
			}
		}
		if (CollUtil.isNotEmpty(sysFileList)) {
			List<List<SysFileLogDTO>> split = ListUtil.split(sysFileList, 500);
			for (List<SysFileLogDTO> fileLogDTOS : split) {
				remoteFileService.batchSave(fileLogDTOS);
			}
		}
	}

	@Override
	public CosPropertiesDTO getCosInfo() {
		log.info("获取cos信息");
		CosPropertiesDTO cosPropertiesDTO = new CosPropertiesDTO();
		BeanUtil.copyProperties(properties.getOss(), cosPropertiesDTO);
		String region = properties.getOss().getRegion();
		if (StrUtil.isBlank(region)) {
			region = properties.getOss().getEndpoint().split("\\.")[1];
		}
		cosPropertiesDTO.setRegion(region);
		cosPropertiesDTO.setBucketName(properties.getBucketName());
		return cosPropertiesDTO;
	}

	/**
	 * 检查压缩包名称是否重复
	 * @param fileName
	 * @return
	 */
	@Override
	public Boolean checkZipFileName(String fileName) {
		R<SysFileVO> fileDataR = remoteFileService.getFileData(fileName);
		if (ObjUtil.isNull(fileDataR) || !fileDataR.isOk()) {
			log.info("[saveGoodsZipInfo] 调用admin系统异常");
			throw GoodsException.build(GoodsBizErrorEnum.GOODS_OUT_SYSTEM_ERROR);
		}
		return ObjUtil.isNull(fileDataR.getData());
	}



	private void updateDecompressFile(Long fileId, String jobId, String code) {
		SysFileVO sysFileVO = new SysFileVO();
		sysFileVO.setId(fileId);
		updateDecompressFile(sysFileVO,jobId,code);
	}
	private void updateDecompressFile(SysFileVO dbZipFileR, String jobId, String code) {
		if (StrUtil.isBlank(jobId)) {
			return;
		}
		String remark = code + "_" +jobId;
		dbZipFileR.setRemark(remark);
		remoteFileService.updateFile(dbZipFileR);
	}

	@Override
    public FileConfParamsVO findFileConfParams(String uploadType) {
        FileConfParamsVO config = new FileConfParamsVO();
        if (StrUtil.isBlank(uploadType)) {
            return config;
        }

        R<List<SysDictItem>> dict = remoteDictService.getInnerDictByType(DictEnum.YYT_FILE_UPLOAD_PARAMS_SET.getCode());
        if (dict == null || dict.getData() == null || CollUtil.isEmpty(dict.getData())) {
            return config;
        }

        Optional<SysDictItem> first = dict.getData().stream().filter(v -> v.getItemValue().equals(uploadType)).findFirst();
        if (first.isEmpty() || StrUtil.isBlank(first.get().getDescription())) {
            return config;
        }

        String delimiter = StrUtil.BACKSLASH + StrUtil.BRACKET_START + StrUtil.AT + StrUtil.BACKSLASH + StrUtil.BRACKET_END;
        String[] configArr = first.get().getDescription().split(delimiter);

        // 超过2个参数
        config.setDir(configArr[0].trim());
        if (configArr.length >= 2) {
            String groupIdStr = configArr[1].trim();
            config.setGroupId(NumberUtil.isLong(groupIdStr) ? Long.valueOf(groupIdStr) : null);
            return config;
        }

        if (NumberUtil.isLong(config.getDir())) {
            config.setGroupId(Long.valueOf(config.getDir()));
        }

        return config;
    }

    private String toOssShowUrl(String dir, String fileName) {
        String endpoint = properties.getOss().getEndpoint();
        endpoint = Validator.isUrl(endpoint) ? URLUtil.url(endpoint).getHost() : endpoint;
        String baseUrl = HTTPS + StrUtil.COLON + StrUtil.SLASH + StrUtil.SLASH + properties.getBucketName() + StrUtil.DOT + endpoint;
        return String.join(StrUtil.SLASH, baseUrl, dir, fileName);
    }

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	static class FileInfoTemp{
		private String fileName;
		private String fileType;
		private InputStream inputStream;
		private String goodsName;
		private String goodsNo;
	}
}
