package com.yts.yyt.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yts.yyt.user.api.entity.UserAuthenticationEntity;
import com.yts.yyt.user.api.vo.UserAuthenticationVO;

import java.util.List;

import org.springframework.web.bind.annotation.RequestParam;

public interface UserAuthenticationService extends IService<UserAuthenticationEntity> {

    /**
     * 添加认证信息(用户该类型唯一)
     *
     * @param userId  用户ID
     * @param authType 认证类型
     */
    void addAuthentication(Long userId, String authType);

    /**
     * 添加认证信息(用户相对该类型ID唯一)
     *
     * @param userId    用户ID
     * @param authType 认证类型
     * @param authId   认证业务ID
     */
    void addAuthentication(Long userId, String authType, String authId);

    /**
     * 获取用户已认证的业务类型(只返回大类型唯一的数据)
     *
     * @param userId    用户ID
     */
    List<String> queryAuthentication(Long userId);
    
    /**
     * 根据用户id及类型查询
     * @param userId
     * @param authType
     * @return
     */
    UserAuthenticationVO getUserAuthentication(Long userId,String authType);

	/**
	 * 根据用户id和认证类型 @See UserAuthenticationEnum 获取列表
	 * @param userIds 用户id
	 * @param authType 认证类型
	 * @return
	 */
	List<UserAuthenticationEntity> getAuthList(List<Long> userIds, String authType);
}