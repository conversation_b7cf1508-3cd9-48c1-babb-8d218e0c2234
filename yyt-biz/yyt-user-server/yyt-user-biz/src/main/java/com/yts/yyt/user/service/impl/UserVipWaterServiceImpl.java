package com.yts.yyt.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yts.yyt.user.api.entity.UserVipWaterEntity;
import com.yts.yyt.user.mapper.UserVipWaterMapper;
import com.yts.yyt.user.service.UserVipWaterService;
import org.springframework.stereotype.Service;

@Service
public class UserVipWaterServiceImpl extends ServiceImpl<UserVipWaterMapper, UserVipWaterEntity>
        implements UserVipWaterService {

    @Override
    public boolean checkBizNoExists(String bizNo) {
        return this.count(new LambdaQueryWrapper<UserVipWaterEntity>()
                .eq(UserVipWaterEntity::getBizNo, bizNo)) > 0;
    }
    
    @Override
    public boolean checkBizNoAndOperationExists(String bizNo, String operationType) {
        return this.count(new LambdaQueryWrapper<UserVipWaterEntity>()
                .eq(UserVipWaterEntity::getBizNo, bizNo)
                .eq(UserVipWaterEntity::getOperationType, operationType)) > 0;
    }
}