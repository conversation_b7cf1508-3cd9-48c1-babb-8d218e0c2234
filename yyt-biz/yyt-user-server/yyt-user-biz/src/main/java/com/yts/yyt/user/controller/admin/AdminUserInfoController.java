package com.yts.yyt.user.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yts.yyt.common.core.dto.IdDTO;
import com.yts.yyt.common.core.util.R;
import com.yts.yyt.common.security.annotation.HasPermission;
import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.swagger.base.BaseController;
import com.yts.yyt.user.api.dto.AppRegisterDTO;
import com.yts.yyt.user.api.dto.UserInfoUpdateDTO;
import com.yts.yyt.user.api.dto.UserVipInfoDTO;
import com.yts.yyt.user.api.vo.UserAccountDetailVO;
import com.yts.yyt.user.api.vo.UserVipInfoDetailVO;
import com.yts.yyt.user.api.vo.UserVipInfoVO;
import com.yts.yyt.user.service.UserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @date 2025-01-06 20:45:45
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(description = "/admin/userInfo", name = "用户信息表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AdminUserInfoController extends BaseController {

    private final UserInfoService userInfoService;
    /**
     * 分页查询
     * @param page 分页对象
     * @param dto 用户余额提现
     * @return
     */
    @Operation(summary = "后台：会员列表分页查询" , description = "会员列表分页查询" )
    @GetMapping("/pageUserVip" )
    @PreAuthorize("@pms.hasPermission('user_vipList_view')" )
    public R<IPage<UserVipInfoVO>> pageUserVipList(@ParameterObject Page page, @ParameterObject UserVipInfoDTO dto) {
        return R.ok(userInfoService.pageUserVipList(page, dto));
    }

    /**
     * 功能 用户会员详情查询
     * 创建于 2025/3/7 13:49
     * <AUTHOR>
     * @param userId 用户ID
     * @return
     */
    @Operation(summary = "后台：用户会员详情查询" , description = "用户会员详情查询" )
    @GetMapping("/userVipDetail/{userId}" )
    @PreAuthorize("@pms.hasPermission('user_vipList_view')" )
    public R<UserVipInfoDetailVO> userVipDetail(@PathVariable Long userId) {
        return R.ok(userInfoService.userVipDetail(userId));
    }

    /**
     * 功能 用户会员详情查询-账户信息
     * @param dto 用户ID
     * @return 用户会员详情查询-账户信息
     */
    @Operation(summary = "后台：用户会员详情查询-账户信息" , description = "用户会员详情查询-账户信息 权限和会员详情接口一致:user_vipList_view")
    @PostMapping("/accountDetail" )
    @PreAuthorize("@pms.hasPermission('user_vipList_view')" )
    public R<UserAccountDetailVO> accountDetail(@RequestBody IdDTO  dto) {
        return R.ok(userInfoService.accountDetail(Long.valueOf(dto.getId())));
    }

    /**
     * 功能 用户会员详情页编辑用户信息
     * 创建于 2025/3/14 09:44
     * <AUTHOR>
     * @param dto 入参
     * @return Boolean
     */
    @Operation(summary = "后台：用户会员详情页编辑用户信息" , description = "用户会员详情页编辑用户信息" )
    @PostMapping("/updateUserInfo" )
    @PreAuthorize("@pms.hasPermission('user_info_update')" )
    public R<Boolean> updateUserInfo(@Valid @RequestBody UserInfoUpdateDTO dto){
        return R.ok(userInfoService.updateUserInfo(dto));
    }

    /**
     * 功能 用户禁用启用
     * 创建于 2025/3/24 18:31
     * <AUTHOR>
     * @param userId 用户ID
     * @param status 状态（1正常 0禁用）
     * @return Boolean
     */

    @Operation(summary = "后台：用户禁用启用" , description = "用户禁用启用" )
    @GetMapping("/disableEnable/{userId}/{status}" )
	@HasPermission("user_member_edit")
    public R<Boolean> disableEnable(@PathVariable Long userId,@PathVariable Integer status){
        return R.ok(userInfoService.disableEnable(userId, status));
    }

    /**
     * 后台-新增合伙人/商家时-新增App用户
     */
	@Inner
    @Operation(summary = "后台-新增合伙人/商家时-新增App用户" , description = "后台-新增合伙人/商家时-新增App用户" )
    @PostMapping("/admin/userInfo/addAppUser" )
    public R<Long> addAppUser(@RequestBody AppRegisterDTO params){
		Long userId = userInfoService.addAppUserForMerchant(params);
		return R.ok(userId);
    }

}


