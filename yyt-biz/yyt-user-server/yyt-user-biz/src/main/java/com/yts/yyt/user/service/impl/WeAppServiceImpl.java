package com.yts.yyt.user.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.yts.yyt.common.core.constant.CacheConstants;
import com.yts.yyt.common.core.constant.SecurityConstants;
import com.yts.yyt.common.core.constant.enums.LoginTypeEnum;
import com.yts.yyt.user.api.constant.enums.SubscribeMsgTplEnum;
import com.yts.yyt.user.api.constant.enums.UserInfoStatusEnum;
import com.yts.yyt.user.api.dto.WeAppSubscribeMsgDTO;
import com.yts.yyt.user.api.entity.AppSocialConfig;
import com.yts.yyt.user.api.exception.UserBizErrorCodeEnum;
import com.yts.yyt.user.api.exception.UserException;
import com.yts.yyt.user.api.vo.*;
import com.yts.yyt.user.service.AppSocialConfigService;
import com.yts.yyt.user.service.UserInfoService;
import com.yts.yyt.user.service.UserSubscribeMessageService;
import com.yts.yyt.user.service.WeAppService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018年08月16日
 */
@Slf4j
@AllArgsConstructor
@Service("weAppService")
public class WeAppServiceImpl implements WeAppService {

    private final UserSubscribeMessageService userSubscribeMessageService;

    private final StringRedisTemplate redisTemplate;

    private final AppSocialConfigService appSocialConfigService;

    private final UserInfoService userInfoService;

    @Override
    public String getAccessToken() {
        String cacheKey = CacheConstants.GLOBALLY.concat(CacheConstants.WX_ACCESS_TOKEN);
        String accessToken = redisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isNotBlank(accessToken)) {
            return accessToken;
        }

        return activeGetAccessToken();
    }

    /**
     * 功能 获取微信小程序AccessToken
     * 创建于 2025/3/7 11:14
     * <AUTHOR>
     * @return String
     */
    private String activeGetAccessToken(){
        String cacheKey = CacheConstants.GLOBALLY.concat(CacheConstants.WX_ACCESS_TOKEN);

        // 获取微信小程序配置
        AppSocialConfig config = appSocialConfigService.getOneConfigByType(LoginTypeEnum.MINI_APP.getType());
        if (config == null || StrUtil.isBlank(config.getAppId()) || StrUtil.isBlank(config.getAppSecret())) {
            log.error("[获取微信小程序AccessToken] 微信小程序配置不正确:({})", config == null ? null : config.getId());
            return null;
        }

        // 重新获取token
        String tokenUrl = String.format(SecurityConstants.MINI_APP_TOKEN_URL, config.getAppId(), config.getAppSecret());
        String tokenResult = HttpUtil.get(tokenUrl);
        log.info("[获取微信小程序AccessToken] 响应报文:{}", tokenResult);
        WeAppAccessTokenVO tokenVO = JSONUtil.toBean(tokenResult, WeAppAccessTokenVO.class);

        // 缓存数据,并返回
        if (tokenVO != null && StrUtil.isNotBlank(tokenVO.getAccess_token())) {
            log.info("[获取微信小程序AccessToken] 获取微信小程序AccessToken:{} 进行缓存", tokenVO.getAccess_token());
            redisTemplate.opsForValue().set(cacheKey, tokenVO.getAccess_token(), tokenVO.getExpires_in() - 30, TimeUnit.SECONDS);
            return tokenVO.getAccess_token();
        }
        return null;
    }

    @Override
    public String getOpenId(String code) {
        // 获取微信小程序配置
        AppSocialConfig config = appSocialConfigService.getOneConfigByType(LoginTypeEnum.MINI_APP.getType());
        if (config == null || StrUtil.isBlank(config.getAppId()) || StrUtil.isBlank(config.getAppSecret())) {
            log.error("[根据code获取微信小程序openId] 微信小程序配置不正确:({})", config == null ? null : config.getId());
            UserBizErrorCodeEnum.WE_APP_CONFIG_ERROR.throwException();
        }

        // 获取小程序对应的openid
        String authCodeUrl = SecurityConstants.MINI_APP_AUTHORIZATION_CODE_URL;
        String result = HttpUtil.get(String.format(authCodeUrl, config.getAppId(), config.getAppSecret(), code));
        log.info("[根据code获取微信小程序openId] code:{} , 响应报文:{}", code, result);


        if (JSONUtil.isTypeJSONObject(result)) {
            WeAppOpenIdVO resVO = JSONUtil.toBean(result, WeAppOpenIdVO.class);
            if (StrUtil.isNotBlank(resVO.getOpenid())) {
                return resVO.getOpenid();
            }

            // mock处理数据
            if (code.startsWith(SecurityConstants.PIGX_PREFIX)) {
                return "wx_" + code.substring(SecurityConstants.PIGX_PREFIX.length());
            }
        }

        UserBizErrorCodeEnum.WE_APP_GET_OPENID_ERROR.throwException();
        return null;
    }

    @Override
    public WeAppPhoneNumberVO getPhoneNumber(String code) {
        // 获取 access_token
        String accessToken = getAccessToken();
        if (StrUtil.isBlank(accessToken)) {
            UserBizErrorCodeEnum.APP_CONTENT_ERROR.throwException();
        }

        // 组装参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);

        // 通过access_token、 code 获取手机号
        String result = HttpUtil.createPost(String.format(SecurityConstants.MINI_APP_PHONE_NUMBER_URL, accessToken))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .body(JSONUtil.toJsonStr(paramMap))
                .execute()
                .body();

        // DC 2025/3/7 10:32 如果报错信息中包含 access_token is invalid 无效，则重新获取一次 access_token
        if(result.contains("access_token is invalid") || result.contains("40001")){
            accessToken = activeGetAccessToken();
            if (StrUtil.isBlank(accessToken)) {
                UserBizErrorCodeEnum.APP_CONTENT_ERROR.throwException();
            }
            result = HttpUtil.createPost(String.format(SecurityConstants.MINI_APP_PHONE_NUMBER_URL, accessToken))
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .body(JSONUtil.toJsonStr(paramMap))
                    .execute()
                    .body();
        }
        
        log.info("[获取微信小程序手机号] 手机号：{}，响应报文:{}",code, result);
        return JSONUtil.toBean(result, WeAppPhoneNumberVO.class);
    }

    @Override
    public Boolean sendSubscribeMsg(Long userId, SubscribeMsgTplEnum tplEnum, String page, Map<String, String> data) {
        return sendSubscribeMsg(userId, tplEnum.getTplId(), page, data);
    }

    @Override
    public Boolean sendSubscribeMsg(Long userId, String tplId, String page, Map<String, String> data) {
        log.info("[发送微信小程序订阅消息] 开始发送订阅消息:({} {} {}) 参数:{}", userId, tplId, page, JSONUtil.toJsonStr(data));
        // 查询用户是否有openid
        UserInfoVO userInfo = userInfoService.getUserInfoVOById(userId);
        if (userInfo == null || StrUtil.isBlank(userInfo.getWeappOpenid()) || !Objects.equals(userInfo.getStatus(), UserInfoStatusEnum.NORMAL.getStatus())) {
            log.info("[发送微信小程序订阅消息] 数据不完整,无法发送订阅消息:({} {})", userId, userInfo == null);
            return false;
        }

        // 检查用户是否订阅消息
        if (!userSubscribeMessageService.checkBindSubscribeMessageTpl(userId, tplId)) {
            log.info("[发送微信小程序订阅消息] 用户({})未订阅消息({}),无法发送订阅消息", userId, tplId);
            return false;
        }

        // 获取access_token
        String accessToken = getAccessToken();
        if (StrUtil.isBlank(accessToken)) {
            log.info("[发送微信小程序订阅消息] 获取微信小程序AccessToken失败,无法发送订阅消息:({} {})", userId, tplId);
            return false;
        }

        // 组装参数
        WeAppSubscribeMsgDTO params = new WeAppSubscribeMsgDTO();
        params.setTouser(userInfo.getWeappOpenid());
        params.setData(covertDataMap(data));
        params.setTemplate_id(tplId);
        params.setPage(page);

        // 请求URL
        String sendMsgUrl = String.format(SecurityConstants.MINI_APP_SEND_SUBSCRIBE_MESSAGE_URL, accessToken);

        // 发送请求
        for (int i = 0; i < 3; i++) {
            try {
                String result = HttpUtil.post(sendMsgUrl, JSONUtil.toJsonStr(params));
                log.info("[发送微信小程序订阅消息] 响应报文:{}", result);
                WeAppSubscribeMsgVO msgResult = JSONUtil.toBean(result, WeAppSubscribeMsgVO.class);

                // 发送成功
                if (Objects.equals(msgResult.getErrcode(), 0)) {
                    return true;
                }

                // 用户拒收订阅消息
                // {"errcode":43101,"errmsg":"user refuse to accept the msg rid: 678658b1-381f3c67-39eb403d"}
                if (Objects.equals(msgResult.getErrcode(), 43101)) {
                    userSubscribeMessageService.unbindSubscribeMessageTpl(userId, tplId);
                    return false;
                }

                return false;
            } catch (Exception e) {
                log.error("[发送微信小程序订阅消息] {} 次数, 发送失败:{}", i, e.getMessage());
            }
        }
        return false;
    }

    private Map<String, Map<String, String>> covertDataMap(Map<String, String> source) {
        Map<String, Map<String, String>> target = new HashMap<>();
        if (source != null && !source.isEmpty()) {
            source.forEach((key, value) -> target.put(key, new HashMap<>() {{
                put("value", value);
            }}));
        }
        return target;
    }
}
