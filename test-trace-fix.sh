#!/bin/bash

# 测试修复后的RocketMQ TraceId拦截器

BASE_URL="http://localhost:7050/test/simple-rocketmq"

echo "=== 测试修复后的RocketMQ TraceId拦截器 ==="
echo ""

# 单次测试
echo "1. 发送测试消息..."
response=$(curl -s -X POST "$BASE_URL/test-basic?content=修复测试消息")
echo "响应: $response"
echo ""

echo "2. 等待3秒让消息被消费..."
sleep 3

echo "3. 再发送一条Message类型的消息..."
response=$(curl -s -X POST "$BASE_URL/test-message?content=修复Message测试")
echo "响应: $response"
echo ""

echo "4. 等待3秒..."
sleep 3

echo "=== 测试完成 ==="
echo ""
echo "请查看应用日志，重点关注："
echo "1. 生产者端是否有DEBUG日志：'拦截到EnvAwareRocketMQTemplate的xxx方法'"
echo "2. 消费者端是否显示：'TraceId提取检查: 通过'"
echo "3. 消费者端是否显示：'TraceId来源: Spring Message Headers'"
echo ""
echo "如果看到'TraceId来源: Spring Message Headers'，说明拦截器修复成功！"
