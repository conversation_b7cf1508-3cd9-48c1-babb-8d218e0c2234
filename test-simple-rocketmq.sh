#!/bin/bash

# 简化版RocketMQ TraceId 拦截器测试脚本

BASE_URL="http://localhost:7050/test/simple-rocketmq"

echo "=== 简化版RocketMQ TraceId 拦截器测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 检查服务状态
echo "1. 检查服务健康状态..."
response=$(curl -s "$BASE_URL/health")
echo "健康检查响应: $response"

if [[ $response == *"ok"* ]]; then
    echo "✅ 服务健康"
else
    echo "❌ 服务异常，请检查服务状态"
    exit 1
fi

echo ""

# 检查TraceId状态
echo "2. 检查当前TraceId状态..."
trace_status=$(curl -s "$BASE_URL/trace-status")
echo "TraceId状态: $trace_status"
echo ""

# 测试1: 基础convertAndSend
echo "3. 测试基础convertAndSend方法..."
response=$(curl -s -X POST "$BASE_URL/test-basic?content=基础测试消息")
echo "响应: $response"

if [[ $response == *"发送成功"* ]]; then
    echo "✅ 基础测试通过"
else
    echo "❌ 基础测试失败"
fi

echo ""
sleep 1

# 测试2: Message对象发送
echo "4. 测试Message对象发送..."
response=$(curl -s -X POST "$BASE_URL/test-message?content=Message测试")
echo "响应: $response"

if [[ $response == *"发送成功"* ]]; then
    echo "✅ Message测试通过"
else
    echo "❌ Message测试失败"
fi

echo ""
sleep 1

# 测试3: 无TraceId情况
echo "5. 测试无TraceId情况..."
response=$(curl -s -X POST "$BASE_URL/test-no-trace?content=无TraceId测试")
echo "响应: $response"

if [[ $response == *"发送成功"* ]]; then
    echo "✅ 无TraceId测试通过"
else
    echo "❌ 无TraceId测试失败"
fi

echo ""

# 快速批量测试
echo "6. 快速批量测试（5条消息）..."
success_count=0
for i in {1..5}; do
    response=$(curl -s -X POST "$BASE_URL/test-basic?content=批量测试$i")
    if [[ $response == *"发送成功"* ]]; then
        ((success_count++))
        echo "  消息$i: ✅"
    else
        echo "  消息$i: ❌"
    fi
    sleep 0.2
done

echo "批量测试结果: $success_count/5 成功"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 验证要点："
echo "1. 查看应用日志中的拦截器DEBUG日志："
echo "   'DEBUG - 拦截到EnvAwareRocketMQTemplate的convertAndSend方法'"
echo ""
echo "2. 查看消费者日志中的TraceId信息："
echo "   'INFO - === RocketMQ TraceId 拦截器测试 ==='"
echo "   'INFO - TraceId一致性检查: 通过'"
echo ""
echo "3. 如果没有看到预期日志，请检查："
echo "   - 拦截器是否正确加载"
echo "   - 日志级别是否设置为DEBUG"
echo "   - AOP配置是否正确"
echo ""
echo "🔧 快速排查命令："
echo "curl -s $BASE_URL/health"
echo "curl -s $BASE_URL/trace-status"
