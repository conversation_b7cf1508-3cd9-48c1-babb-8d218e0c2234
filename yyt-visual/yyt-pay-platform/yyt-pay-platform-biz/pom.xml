<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yts</groupId>
        <artifactId>yyt-pay-platform</artifactId>
        <version>5.7.0</version>
    </parent>

    <artifactId>yyt-pay-platform-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <yungouos.version>2.0.4</yungouos.version>
    </properties>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-3-starter</artifactId>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-sequence</artifactId>
        </dependency>

        <!--必备：脱敏工具类-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-sensitive</artifactId>
        </dependency>

        <!--灰度支持-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-gray</artifactId>
        </dependency>
        <!-- sentinel-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-sentinel</artifactId>
        </dependency>
        <!--feign 接口-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-upms-api</artifactId>
        </dependency>
        <!--swagger-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-swagger</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-xss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-job</artifactId>
        </dependency>
        <!-- 支付依赖-->
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-WxPay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-AliPay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yungouos.pay</groupId>
            <artifactId>yungouos-pay-sdk</artifactId>
            <version>${yungouos.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-pay-platform-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-order-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-user-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-distribution-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.15</version>
        </dependency>


        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-seata</artifactId>
        </dependency>
        <!--core 工具类-->
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-merchant-api</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-ticket-api</artifactId>
            <version>5.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.yts</groupId>
            <artifactId>yyt-common-alert</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources/</directory>
                        <includes>
                            <include>**/*</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <includeSystemScope>true</includeSystemScope>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                                <configuration>
                                    <loaderImplementation>CLASSIC</loaderImplementation>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>io.fabric8</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <configuration>
                            <skip>false</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>boot</id>
        </profile>
    </profiles>

</project>
