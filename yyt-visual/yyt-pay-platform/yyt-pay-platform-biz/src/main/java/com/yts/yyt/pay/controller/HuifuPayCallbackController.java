package com.yts.yyt.pay.controller;

import com.yts.yyt.common.security.annotation.Inner;
import com.yts.yyt.common.xss.core.XssCleanIgnore;
import com.yts.yyt.pay.handler.EncashmentNotifyCallbackHandler;
import com.yts.yyt.pay.handler.PayNotifyCallbackHandler;
import com.yts.yyt.pay.handler.RefundNotifyCallbackHandler;
import com.yts.yyt.pay.utils.PayNotifyTypeEnum;
import com.yts.yyt.pay.utils.RefundNotifyTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class HuifuPayCallbackController {


    private final Map<String, PayNotifyCallbackHandler> payNotifyCallbackHandlerMap;

	private final Map<String, RefundNotifyCallbackHandler> refundNotifyCallbackHandler;

	private final Map<String, EncashmentNotifyCallbackHandler> encashmentNotifyCallbackHandler;

	/**
     * 聚合正扫异步回调
     * 
     * @param payOrderId
     * @param resp_data
     * @param resp_code
     * @param sign
     * @see <a href="https://paas.huifu.com/open/doc/api/#/smzf/api_jhzs_v3">...</a>
     * @return
     */
    @Inner(false)
    @SneakyThrows
    @XssCleanIgnore
    @PostMapping("/hf/jspay/callback/{id}")
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public String handleJspayCallback(@PathVariable("id") Long payOrderId,
            @RequestParam("resp_data") String resp_data,
            @RequestParam("resp_code") String resp_code,
            @RequestParam("sign") String sign) {
        log.info("汇付支付回调,data:{}  , id: {} ,resp_code:{} ", resp_data, payOrderId, resp_code);

        Map<String, Object> params = new HashMap<>();
        params.put("id",payOrderId);
        params.put("resp_data",resp_data);
        params.put("resp_code",resp_code);
        params.put("sign",sign);
        return payNotifyCallbackHandlerMap.get(PayNotifyTypeEnum.HUIFU_PAY_NOTIFY.getName()).handle(params);
    }

    /**
     * 扫码交易退款
     * 
     * @param resp_data
     * @param resp_code
     * @param sign
     * @see <a href="https://paas.huifu.com/open/doc/api/#/smzf/api_jhzs_v3">...</a>
     * @return
     */
    @Inner(false)
    @SneakyThrows
    @XssCleanIgnore
    @PostMapping("/hf/scanpay/refund/callback/{id}")
    @GlobalTransactional
	@Transactional(rollbackFor = Exception.class)
    public String handleDelayTransConfirmrefundCallback(@PathVariable("id") String refundOrderId,
            @RequestParam("resp_data") String resp_data,
            @RequestParam("resp_code") String resp_code,
            @RequestParam("sign") String sign) {
		log.info("汇付支付扫码交易退款回调开始,data:{} , id:{} ,resp_code:{} ", resp_data, refundOrderId, resp_code);

		Map<String, Object> params = new HashMap<>();
		params.put("refundOrderId",refundOrderId);
		params.put("resp_data",resp_data);
		params.put("resp_code",resp_code);
		params.put("sign",sign);
		return refundNotifyCallbackHandler.get(RefundNotifyTypeEnum.HUIFU_REFUND_NOTIFY.getName()).handle(params);
    }

	/**
	 * 取现
	 *
	 * @param resp_data
	 * @param resp_code
	 * @param sign
	 * @see <a href="https://paas.huifu.com/open/doc/api/#/smzf/api_jhzs_v3">...</a>
	 * @return
	 */
	@Inner(false)
	@SneakyThrows
	@XssCleanIgnore
	@PostMapping("/hf/encashment/callback/{id}")
	@GlobalTransactional
	@Transactional(rollbackFor = Exception.class)
	public String handleEncashmentCallback(@PathVariable("id") String bizId,
														@RequestParam("resp_data") String resp_data,
														@RequestParam("resp_code") String resp_code,
														@RequestParam("sign") String sign) {
		log.info("汇付主动取现,data:{} , id:{} ,resp_code:{} ", resp_data, bizId, resp_code);

		Map<String, Object> params = new HashMap<>();
		params.put("bizId", bizId);
		params.put("resp_data", resp_data);
		params.put("resp_code", resp_code);
		params.put("sign", sign);

		return encashmentNotifyCallbackHandler.get("HUIFU_ENCASHMENT_NOTIFY").handle(params);
	}
}
