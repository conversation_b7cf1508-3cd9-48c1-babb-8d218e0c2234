package com.yts.yyt.pay.handler.prepay;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.app.AppServiceExtension;
import com.wechat.pay.java.service.payments.app.model.Amount;
import com.wechat.pay.java.service.payments.app.model.PrepayRequest;
import com.yts.yyt.pay.entity.PayChannel;
import com.yts.yyt.pay.entity.PayTradeOrder;
import com.yts.yyt.pay.mapper.PayChannelMapper;
import com.yts.yyt.pay.utils.ChannelPayApiConfigKit;
import com.yts.yyt.pay.utils.PayChannelNameEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>2025-01-13</p>
 * <p>微信APP支付 V3.</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service("WEIXIN_APP_PAY")
@RequiredArgsConstructor
public class WeChatAppPayV3Handler extends AbstractPayOrderHandler {

    private final PayChannelMapper channelMapper;

    @Override
    public PayChannel preparePayParams () {
        PayChannelNameEnum channelEnum = PayChannelNameEnum.WEIXIN_APP_PAY;
        PayChannel channel = channelMapper.selectOne(Wrappers.<PayChannel>lambdaQuery().eq(PayChannel::getChannelId, channelEnum.getName()));
        if (channel == null) {
            throw new IllegalArgumentException("微信APP支付渠道配置为空");
        }
        return channel;
    }

    @Override
    public PayTradeOrder createTradeOrder (PayTradeOrder tradeOrder) {
        tradeOrder.setChannelId(PayChannelNameEnum.WEIXIN_APP_PAY.getName());
        BigDecimal amt = new BigDecimal(tradeOrder.getAmount());
        tradeOrder.setAmount(amt.multiply(new BigDecimal("100")).toString());
        return super.createTradeOrder(tradeOrder);
    }

    @Override
    public Object pay (PayTradeOrder tradeOrder) {
        PayChannel channel = ChannelPayApiConfigKit.get();
        JSONObject params = JSONUtil.parseObj(channel.getParam());

        AppServiceExtension service = new AppServiceExtension.Builder().config(new RSAAutoCertificateConfig.Builder()
                .merchantId(channel.getChannelMchId())
                .privateKeyFromPath(params.getStr("privateKeyFromPath"))
                .merchantSerialNumber(params.getStr("merchantSerialNumber"))
                .apiV3Key(params.getStr("apiV3Key"))
                .build()).build();

        PrepayRequest prepayRequest = new PrepayRequest();
        prepayRequest.setMchid(channel.getChannelMchId());
        prepayRequest.setAppid(channel.getAppId());
        prepayRequest.setOutTradeNo(String.valueOf(tradeOrder.getOrderId()));
        prepayRequest.setAttach(new JSONObject().putOnce("payOrderId", tradeOrder.getOrderId()).toString());
        prepayRequest.setDescription(tradeOrder.getSubject());
        Amount amount = new Amount();
        amount.setTotal(new BigDecimal(tradeOrder.getAmount()).intValue());
        amount.setCurrency(tradeOrder.getCurrency());
        prepayRequest.setAmount(amount);
        prepayRequest.setNotifyUrl(channel.getNotifyUrl());
        tradeOrder.setParam1(JSONUtil.toJsonStr(prepayRequest));

        return new Gson().toJson(service.prepayWithRequestPayment(prepayRequest));
    }

    @Override
    public void updateOrder (Object result, PayTradeOrder tradeOrder) {
//        PrepayWithRequestPaymentResponse response = (PrepayWithRequestPaymentResponse) result;
        // 结果处理
        super.updateOrder(result, tradeOrder);
    }
}
