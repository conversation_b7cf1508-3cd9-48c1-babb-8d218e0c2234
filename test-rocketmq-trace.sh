#!/bin/bash

# RocketMQ TraceId 拦截器测试脚本
# 用于快速测试拦截器功能是否正常

BASE_URL="http://localhost:7050/test/rocketmq-trace"

echo "=== RocketMQ TraceId 拦截器测试开始 ==="
echo "基础URL: $BASE_URL"
echo ""

# 检查服务是否启动
echo "1. 检查服务状态..."
curl -s "$BASE_URL/current-trace-id" > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 服务已启动"
else
    echo "❌ 服务未启动，请先启动 yyt-distribution-server"
    exit 1
fi

echo ""

# 测试1: convertAndSend方法
echo "2. 测试 convertAndSend 方法..."
response=$(curl -s -X POST "$BASE_URL/test-convert-send?content=测试convertAndSend")
echo "响应: $response"
if [[ $response == *"发送成功"* ]]; then
    echo "✅ convertAndSend 测试通过"
else
    echo "❌ convertAndSend 测试失败"
fi

echo ""
sleep 2

# 测试2: send方法
echo "3. 测试 send 方法..."
response=$(curl -s -X POST "$BASE_URL/test-send-message?content=测试send方法")
echo "响应: $response"
if [[ $response == *"发送成功"* ]]; then
    echo "✅ send 测试通过"
else
    echo "❌ send 测试失败"
fi

echo ""
sleep 2

# 测试3: 延迟消息
echo "4. 测试延迟消息（5秒延迟）..."
response=$(curl -s -X POST "$BASE_URL/test-delay-message?content=测试延迟消息&delaySeconds=5")
echo "响应: $response"
if [[ $response == *"发送成功"* ]]; then
    echo "✅ 延迟消息发送成功，请等待5秒后查看消费日志"
else
    echo "❌ 延迟消息测试失败"
fi

echo ""
sleep 2

# 测试4: 事务消息
echo "5. 测试事务消息..."
response=$(curl -s -X POST "$BASE_URL/test-transaction-message?content=测试事务消息")
echo "响应: $response"
if [[ $response == *"发送成功"* ]]; then
    echo "✅ 事务消息测试通过"
else
    echo "❌ 事务消息测试失败"
fi

echo ""
sleep 2

# 测试5: 无TraceId情况
echo "6. 测试无TraceId情况..."
response=$(curl -s -X POST "$BASE_URL/test-no-trace-id?content=测试无TraceId")
echo "响应: $response"
if [[ $response == *"发送成功"* ]]; then
    echo "✅ 无TraceId测试通过"
else
    echo "❌ 无TraceId测试失败"
fi

echo ""
sleep 2

# 批量测试
echo "7. 批量测试（10条消息）..."
success_count=0
for i in {1..10}; do
    response=$(curl -s -X POST "$BASE_URL/test-convert-send?content=批量测试消息$i")
    if [[ $response == *"发送成功"* ]]; then
        ((success_count++))
    fi
    sleep 0.2
done

echo "批量测试结果: $success_count/10 成功"
if [ $success_count -eq 10 ]; then
    echo "✅ 批量测试通过"
else
    echo "❌ 批量测试部分失败"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📋 验证清单："
echo "1. 查看应用日志，确认生产者端有以下DEBUG日志："
echo "   'DEBUG - 拦截到EnvAwareRocketMQTemplate的xxx方法'"
echo "   'DEBUG - 为EnvAwareRocketMQTemplate.xxx添加traceId'"
echo ""
echo "2. 查看消费者日志，确认有以下INFO日志："
echo "   'INFO - === RocketMQ TraceId 拦截器测试 ==='"
echo "   'INFO - TraceId一致性检查: 通过'"
echo "   'INFO - TraceId提取检查: 通过'"
echo ""
echo "3. 如果看不到上述日志，请检查："
echo "   - AOP配置是否正确 (@EnableAspectJAutoProxy)"
echo "   - 拦截器类是否被Spring扫描到"
echo "   - 日志级别是否设置为DEBUG"
echo ""
echo "🔧 故障排查："
echo "如果测试失败，请检查："
echo "1. RocketMQ服务是否正常运行"
echo "2. Topic是否已创建: \${rocketmq.env}_TP_TEST_ROCKETMQ"
echo "3. 消费者组是否正常: \${rocketmq.env}_CG_TEST_TRACE_INTERCEPTOR"
echo "4. 网络连接是否正常"
echo ""
echo "📊 性能监控："
echo "观察以下指标："
echo "- 消息发送成功率应为100%"
echo "- TraceId传递成功率应为100%"
echo "- 拦截器处理时间应<1ms"
echo "- 无内存泄漏"
