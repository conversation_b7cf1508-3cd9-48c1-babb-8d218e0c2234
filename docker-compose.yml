# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:

  yyt-gateway:
    image: harbor.boyangwenwu.com/yyt/yyt-gateway:${IMAGE_TAG}
    restart: always
    ports:
      - 9999:9999
    container_name: yyt-gateway
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-auth:
    image: harbor.boyangwenwu.com/yyt/yyt-auth:${IMAGE_TAG}
    restart: always
    container_name: yyt-auth
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-upms-biz:
    image: harbor.boyangwenwu.com/yyt/yyt-upms-biz:${IMAGE_TAG}
    restart: always
    container_name: yyt-upms-biz
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-monitor:
    image: harbor.boyangwenwu.com/yyt/yyt-monitor:${IMAGE_TAG}
    restart: always
    ports:
      - 5001:5001
    container_name: yyt-monitor
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-codegen:
    image: harbor.boyangwenwu.com/yyt/yyt-codegen:${IMAGE_TAG}
    restart: always
    container_name: yyt-codegen
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-pay-platform:
    image: harbor.boyangwenwu.com/yyt/yyt-pay-platform:${IMAGE_TAG}
    restart: always
    container_name: yyt-pay-platform
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default

  yyt-mp-platform:
    image: harbor.boyangwenwu.com/yyt/yyt-mp-platform:${IMAGE_TAG}
    restart: always
    container_name: yyt-mp-platform
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
    networks:
      - yyt_cloud_default


  yyt-goods-biz:
    image: harbor.boyangwenwu.com/yyt/yyt-goods-biz:${IMAGE_TAG}
    restart: always
    container_name: yyt-goods-biz
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
      - "yyt-seata:***********"
    networks:
      - yyt_cloud_default


  yyt-order-biz:
    image: harbor.boyangwenwu.com/yyt/yyt-order-biz:${IMAGE_TAG}
    restart: always
    container_name: yyt-order-biz
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
      - "yyt-seata:***********"
    networks:
      - yyt_cloud_default


  yyt-user-biz:
    image: harbor.boyangwenwu.com/yyt/yyt-user-biz:${IMAGE_TAG}
    restart: always
    container_name: yyt-user-biz
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
      - "yyt-seata:***********"
    networks:
      - yyt_cloud_default

  yyt-merchant-biz:
    image: harbor.boyangwenwu.com/yyt/yyt-merchant-biz:${IMAGE_TAG}
    restart: always
    container_name: yyt-merchant-biz
    extra_hosts:
      - "yyt-mysql:***********"
      - "yyt-gateway:***********"
      - "yyt-register:***********"
      - "yyt-redis:***********"
      - "yyt-seata:***********"
    networks:
      - yyt_cloud_default

networks:
  yyt_cloud_default:
    driver: bridge
